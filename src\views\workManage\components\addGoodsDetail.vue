<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="基础属性" name="first">
        <approval-process-new :approvalData="approvalData" :boardData="urlParam"></approval-process-new>
        <spu ref="spu" :spuData="spuData" :skuData="skuData"></spu>
        <sku ref="sku" :skuData="skuData" :sauData="sauData"></sku>
        <image-review
          v-if="isShowPackageInfo"
          ref="imageReview"
          :outPackageImgList="getOutPackageImgList()"
          :directionImgList="getDirectionImgList()"
          :formDisable="true"
          :enableOCR="false"
          :enableDrag="false"
          @change="handleImageReviewChange"
        ></image-review>
        <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false" :formDisable="urlParam.modify == 1 ? false : true"
        :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true">
        </label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'"></extended-attr2>
        <modify-record
          v-if="urlParam.record == 'hide' ? false : true"
          :recordData="recordData"
        ></modify-record>
      </el-tab-pane>
      <el-tab-pane label="资质属性" name="third">
        <qualification-attr></qualification-attr>
      </el-tab-pane>
      <el-tab-pane label="精修图版本" name="fourth">
        <sales-attr :imgTitle="imgTitle"></sales-attr>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import { getAddProductData, getAddProcessData, getAuth } from "@/api/workManage"

import modifyRecord from "@/views/product/modifyRecord";
import qualificationAttr from "./qualificationAttr";
import salesAttr from "./salesAttr";
import imageReview from "@/views/product/imageReview.vue";
export default {
  name: "",
  components: { modifyRecord, qualificationAttr, salesAttr, imageReview },
  mixins: [productMixinBase],
  // 是否展示修改记录
  computed: {
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
    isShowPackageInfo(){
      if(this.$route.query.procKey === '新品上报流程' || this.$route.query.procKey === '商品新增流程'){
        return true
      }
      return false
    },
  },
  data() {
    return {
      recordData: [],
      imgTitle: "",
      activeName: "first",
    };
  },
  created() {
    // 设置商品操作类型为 详情
    this.$store.commit("product/SET_OPERATION_TYPE", "detail");
    // 获取审批流信息
    this.getApplyInfo();
    // 获取商品详情数据
    this.getProductDetail();
    // this.getBtnAuth()
    this.productLoading = false;
  },
  methods: {
    /**
     * 获取外包装图片列表
     */
    getOutPackageImgList() {
      if (this.skuData && this.skuData.length > 0) {
        return this.skuData[0].outPackageImgList || [];
      }
      return [];
    },

    /**
     * 获取说明书图片列表
     */
    getDirectionImgList() {
      if (this.skuData && this.skuData.length > 0) {
        return this.skuData[0].directionImgList || [];
      }
      return [];
    },

    /**
     * 处理图片审核组件数据变化
     */
    handleImageReviewChange(data) {
      // 更新 SKU 数据中的图片信息
      if (this.skuData && this.skuData.length > 0) {
        // 更新外包装图片（主图 + 外包装图片）
        const newOutPackageList = data.combinedPackageList || [];
        const newDirectionList = data.directionImgList || [];

        // 只有在数据真正变化时才更新
        const currentOutPackageList = this.skuData[0].outPackageImgList || [];
        const currentDirectionList = this.skuData[0].directionImgList || [];

        if (JSON.stringify(currentOutPackageList) !== JSON.stringify(newOutPackageList)) {
          this.skuData[0].outPackageImgList = newOutPackageList;
        }

        if (JSON.stringify(currentDirectionList) !== JSON.stringify(newDirectionList)) {
          this.skuData[0].directionImgList = newDirectionList;
        }
      }

      // 触发数据变化事件
      this.$bus.$emit("productChange", true);
    },

    // 获取按钮权限
    async getBtnAuth(){
      let param = {
        procDefId: this.urlParam.procDefId,
        nodeKey: this.urlParam.nodeKey
      }
      try {
        const { res } = await getAuth(param)
        console.log(res);
      }
      catch(error) {
        console.log(error)
      }
    },
    async getProductDetail() {
      this.productLoading = true;
      let param = {
        procInstId:this.urlParam.procInstId,
        applyCode:this.urlParam.applyCode,
        productType:this.urlParam.productType,
        productCode:this.urlParam.productCode,
        detailType:this.urlParam.detailType,
      }
      // let param = {
      //   procInstId:"60182",
      //   applyCode:"XPSB2107000022",
      //   productType:this.urlParam.productType,
      //   productCode:"S1330001",
      //   detailType:this.urlParam.detailType,
      // }
      console.log(param)
      let res = await getAddProductData(param);
      // console.log(res);
      this.spuData = Object.freeze(res.data.spu);
      this.skuData = Object.freeze(res.data.sku);
      this.sauData = Object.freeze(res.data.sau);
      this.recordData = Object.freeze(res.data.record);
      if(!this.skuData[0]) return;
      this.imgTitle = `${this.skuData[0].skuCode}-${this.skuData[0].productId}-${this.skuData[0].smallPackageCode[0]}-${this.skuData[0].skuName}`;
    },
    async getApplyInfo() {
      let param = {
        procDefId: this.urlParam.procDefId,
        procInstId: this.urlParam.procInstId
      };
      let res = await getAddProcessData(param);
      console.log(res);
      if (res.success) {
        this.approvalData = res.data;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content{
    padding: 0 !important;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
    }
  }
  .bottom-btns {
    background: #fff;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
}
</style>
