<template>
  <div class="basic-info-container">
    <div class="basic-info-title" @click="showForm = !showForm">
      基础信息
      <i style="color: #3b95a8" :class="showForm ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      <span class="title-tips" v-if="isCoorection"> <i class="el-icon-s-opportunity"></i> 鼠标移动至高亮字段上可查看纠错值 </span>
    </div>
    <div class="basic-info-form" v-show="showForm">
      <!-- v-loading="spuLoading" -->
      <el-form :disabled="formDisable && !formAuth.length" :model="model" :rules="rules" ref="spuForm" :validate-on-rule-change="false">
        <el-row type="flex">
          <!--商品分类-->
          <el-col :md="8" :sm="12" :xs="24">
            <el-form-item
              data-diff="hhhh"
              prop="spuCategory"
              ref="spuCategory"
              :class="{
                'is-change': changeList.includes('spuCategory') || coorectionType['spuCategory'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['spuCategory']"
                  :disabled="!coorectionType['spuCategory']"
                  placement="top-start"
                >
                  <span>商品大类</span>
                </el-tooltip>
              </template>
              <el-select
                @change="changeSpuCategory"
                v-model="model.spuCategory"
                :disabled="modelAttr.spuCategoryDisabled || formDisable"
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in $store.getters.selectOptions.spuCategoryOptions"
                  :key="'key_spuCategoryOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                  :disabled="!item.isValid"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-popover placement="top" width="200" trigger="hover" :content="$store.getters.info[5] ? $store.getters.info[5].showContent : ''">
              <i class="info-suffix-icon el-icon-question" slot="reference"></i>
            </el-popover>
          </el-col>
          <!--spu编码,商品编码-->
          <el-col :lg="8" :md="8" :xs="24" :sm="12" :xl="8" class="reset-search-button">
            <el-form-item :disabled="formDisable" prop="spuCode" ref="spuCode" v-change="model.spuCode">
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['spuCode']"
                  :disabled="!coorectionType['spuCode']"
                  placement="top-start"
                >
                  <span>{{ model.spuCategory === TYPEENUM.GIFT ? "商品编码" : "SPU编码" }}</span>
                </el-tooltip>
              </template>
              <div class="copy-input-container">
                <div class="copy-input">
                  <el-input v-model="model.spuCode" :disabled="modelAttr.spuCodeDisabled"> </el-input>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton" @click="copyInfo(model.spuCode)">复制</span>
                </div>
              </div>
            </el-form-item>
            <el-popover placement="top" width="200" trigger="hover" :content="$store.getters.info[4] ? $store.getters.info[4].showContent : ''">
              <i class="info-suffix-icon el-icon-question" slot="reference"></i>
            </el-popover>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--通用名-->
          <el-col :md="8" :sm="24" :xs="24">
            <el-form-item
              prop="generalName"
              ref="generalName"
              :class="{
                'is-change': changeList.includes('generalName') || coorectionType['generalName'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['generalName']"
                  :disabled="!coorectionType['generalName']"
                  placement="top-start"
                >
                  <span>通用名</span>
                </el-tooltip>
              </template>
              <div class="copy-input-container">
                <div class="copy-input">
                  <el-input
                    v-model="model.generalName"
                    :disabled="modelAttr.generalNameDisabled || formDisable"
                    @change="editRateFunction()"
                    @blur="changeGeneralName()"
                    size="small"
                  >
                    <el-popover
                      v-if="model.generalName.length > 5"
                      placement="top"
                      width="400"
                      trigger="hover"
                      slot="append"
                      :content="model.generalName"
                    >
                      <i class="el-icon-more" slot="reference"></i>
                    </el-popover>
                  </el-input>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(model.generalName)">复制</span>
                </div>
              </div>
            </el-form-item>
            <el-popover placement="top" width="200" trigger="hover" :content="$store.getters.info[3] ? $store.getters.info[3].showContent : ''">
              <i class="info-suffix-icon el-icon-question" slot="reference"></i>
            </el-popover>
          </el-col>
          <el-col :md="8" :sm="24" :xs="24" v-if="model.spuCategory === TYPEENUM.MEDICAL_INSTRUMENT">
            <el-form-item
              prop="manufacturingLicenseNo"
              ref="manufacturingLicenseNo"
              :class="{
                'is-change': changeList.includes('manufacturingLicenseNo') || coorectionType['manufacturingLicenseNo'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['manufacturingLicenseNo']"
                  :disabled="!coorectionType['manufacturingLicenseNo']"
                  placement="top-start"
                >
                  <span>生产许可证或备案凭证编号</span>
                </el-tooltip>
              </template>
              <el-input maxlength="100" show-word-limit v-model="model.manufacturingLicenseNo" :disabled="modelAttr.manufacturingLicenseNoDisabled || formDisable" size="small">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--批准文号-->
          <el-col :md="8" :sm="24" :xs="24" :offset="8" :pull="8">
            <el-form-item
              prop="approvalNo"
              ref="approvalNo"
              :class="{
                'is-change': changeList.includes('approvalNo') || coorectionType['approvalNo'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['approvalNo']"
                  :disabled="!coorectionType['approvalNo']"
                  placement="top-start"
                >
                  <span>{{ model.businessScopeList.some(item => item.includes(1154)) ? '化妆品备案编号/注册证号' : approvalNoLabel }}</span>
                </el-tooltip>
              </template>
              <div class="copy-input-container">
                <div class="copy-input">
                  <el-input v-model="model.approvalNo" maxlength="300" :disabled="modelAttr.approvalNoDisabled || formDisable" size="small" @blur="approvalNoBlur()">
                    <el-button
                      size="small"
                      v-show="
                        operationType == 'add' ||
                        operationType == 'draft' ||
                        operationType == 'update' ||
                        operationType == 'reuse' ||
                        operationType == 'present'
                      "
                      slot="append"
                      icon="el-icon-search"
                      :disabled="formDisable"
                      @click="spuCodeMultiplexing"
                    ></el-button>
                  </el-input>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(model.approvalNo)">复制</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <!-- 经营范围 -->
          <el-col :md="8" :sm="24" :xs="24" :pull="8">
            <el-form-item
              prop="businessScopeList"
              ref="businessScopeList"
              :class="{
                'is-change': changeList.includes('businessScopeList') || coorectionType['businessScopeList'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['businessScopeList']"
                  :disabled="!coorectionType['businessScopeList']"
                  placement="top-start"
                >
                  <span>所属经营范围</span>
                </el-tooltip>
              </template>
              <el-cascader
                ref="elCascader"
                style="width: 100%"
                v-model="model.businessScopeList"
                :disabled="(modelAttr.businessScopeListDisabled || formDisable) && formAuth.indexOf('businessScopeList') == -1"
                :options="modelAttr.businessScopeListOptions"
                :show-all-levels="false"
                @change="changeBusiness"
                :props="{
                  multiple: true,
                  checkStrictly: true,
                  value: 'id',
                  label: 'dictName',
                  disabled: 'disable',
                }"
                filterable
                size="small"
                :key="resetKey"
              >
                <template slot-scope="{ node, data }">
                  <span>{{ data.dictName }}</span>
                  <span v-if="!node.isLeaf">({{ data.children.length }})</span>
                </template>
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--剂型-->
          <el-col
            :md="8"
            :sm="12"
            :xs="24"
            v-show="
              colToggle(
                [TYPEENUM.EMPTY, TYPEENUM.NOT_MEDICINE, TYPEENUM.MEDICAL_INSTRUMENT, TYPEENUM.TRADITIONAL_MEDICINE, TYPEENUM.GENERAL_MEDICINE],
                'dosageForm'
              )
            "
          >
            <el-form-item
              prop="dosageForm"
              ref="dosageForm"
              :class="{
                'is-change': changeList.includes('dosageForm') || coorectionType['dosageForm'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['dosageForm']"
                  :disabled="!coorectionType['dosageForm']"
                  placement="top-start"
                >
                  <span>剂型</span>
                </el-tooltip>
              </template>
              <el-select
                v-model="model.dosageForm"
                :disabled="(modelAttr.dosageFormDisabled || formDisable) && formAuth.indexOf('dosageForm') == -1"
                placeholder="请选择"
                filterable
                @change="editRateFunction()"
                size="small"
              >
                <el-option
                  v-for="(item, key) in $store.getters.selectOptions.dosageFormOptions"
                  :key="'key_dosageFormOptions_' + item.id + key"
                  :label="item.dictName"
                  :value="item.id"
                  :disabled="!item.isValid"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 存储属性 -->
          <el-col
            :md="8"
            :sm="12"
            :xs="24"
            v-show="
              colToggle(
                [TYPEENUM.EMPTY, TYPEENUM.NOT_MEDICINE, TYPEENUM.MEDICAL_INSTRUMENT, TYPEENUM.TRADITIONAL_MEDICINE, TYPEENUM.GENERAL_MEDICINE],
                'shadingAttr'
              )
            "
          >
            <el-form-item
            :prop="hasShadingAttr()"
              ref="shadingAttr"
              :class="{
                'is-change': changeList.includes('shadingAttr') || coorectionType['shadingAttr'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['shadingAttr']"
                  :disabled="!coorectionType['shadingAttr']"
                  placement="top-start"
                >
                  <span>存储属性</span>
                </el-tooltip>
              </template>
              <el-select
                v-model="model.shadingAttr"
                :disabled="(modelAttr.shadingAttrDisabled || formDisable) && formAuth.indexOf('shadingAttr') == -1"
                placeholder="请选择"
                filterable
                size="small"
              >
                <el-option label="外用" :value="1"></el-option>
                <el-option label="内服" :value="2"></el-option>
                <el-option label="遮光" :value="3"></el-option>
                <el-option label="食品" :value="4"></el-option>
                <el-option label="保健食品" :value="5"></el-option>
                <el-option label="其他" :value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!--品牌厂家-->
          <el-col :md="8" :sm="12" :xs="24" v-if="operationType != 'add'">
            <el-form-item
              prop="brandName"
              ref="brandName"
              :class="{
                'is-change': changeList.includes('brandName') || coorectionType['brandName'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['brandName']"
                  :disabled="!coorectionType['brandName'] || formDisable"
                  placement="top-start"
                >
                  <span>品牌厂家</span>
                </el-tooltip>
              </template>
              <el-input v-model="model.brandName" maxlength="300" disabled size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--生产厂家-->
          <el-col
            :md="8"
            :sm="24"
            :xs="24"
            :offset="operationType != 'add' ? 0 : 1"
            :pull="operationType != 'add' ? 0 : 1"
            style="position: relative"
          >
            <!-- <div class="fix-select-arrow" @click="fixSelectArrowByFilterable('manufacturerSelect')"></div> -->
            <!-- @blur="fixSelectArrowByFilterable('manufacturerSelect','blur')" -->
            <el-form-item
              label="生产厂家"
              prop="manufacturer"
              ref="manufacturer"
              :class="{
                'is-change': changeList.includes('manufacturer') || coorectionType['manufacturer'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['manufacturer']"
                  :disabled="!coorectionType['manufacturer']"
                  placement="top-start"
                >
                  <span>生产厂家</span>
                </el-tooltip>
              </template>
              <div class="manufacturer-select-container">
                <div class="manufacturer-select">
                  <el-select
                    v-model="model.manufacturerName"
                    :filter-method="debounce(searchManufacturer, 300)"
                    @change="manufacturerChange"
                    @visible-change="manufacturerFocus"
                    :disabled="modelAttr.manufacturerDisabled || formDisable"
                    :placeholder="manufacturerPlaceholder"
                    ref="manufacturerSelect"
                    filterable
                    :allow-create="true"
                    size="small"
                    clearable
                    @clear="clearManufacturer"
                  >
                    <el-option
                      v-for="item in modelAttr.manufacturerOptions"
                      :key="'key_manufacturerOptions_' + item.id"
                      :label="item.dictName"
                      :value="item.dictName"
                      :disabled="!item.isValid"
                    ></el-option>
                  </el-select>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(model.manufacturerName)">复制</span>
                </div>
                <div>
                  <el-button type="primary" size="small" @click="addManufacturer" v-if="canAddManufacturer">
                      <i class="el-icon-plus"></i>
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <!--厂家分类-->
          <el-col :md="8" :sm="12" :xs="24" :pull="operationType != 'add' ? 0 : 1">
            <el-form-item
              prop="manufacturerCategoryName"
              ref="manufacturerCategoryName"
              :class="{
                'is-change': changeList.includes('manufacturerCategoryName') || coorectionType['manufacturerCategoryName'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['manufacturerCategoryName']"
                  :disabled="!coorectionType['manufacturerCategoryName']"
                  placement="top-start"
                >
                  <span>厂家分类</span>
                </el-tooltip>
              </template>
              <el-input
                v-model="model.manufacturerCategoryName"
                maxlength="300"
                :disabled="modelAttr.manufacturerCategoryNameDisabled || formDisable"
                size="small"
              ></el-input>
            </el-form-item>
          </el-col>
          <!--上市许可持有人-->
          <el-col :md="8" :sm="24" :xs="24" v-show="colToggle([TYPEENUM.GENERAL_MEDICINE], 'marketAuthor')">
            <el-form-item
              prop="marketAuthor"
              ref="marketAuthor"
              :class="{
                'is-change': changeList.includes('marketAuthor') || coorectionType['marketAuthor'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['marketAuthor']"
                  :disabled="!coorectionType['marketAuthor']"
                  placement="top-start"
                >
                  <span>上市许可持有人</span>
                </el-tooltip>
              </template>
              <div class="copy-input-container">
                <div class="copy-input">
                  <el-input
                    v-model="model.marketAuthor"
                    maxlength="100"
                    :disabled="(modelAttr.marketAuthorDisabled || formDisable) && formAuth.indexOf('marketAuthor') == -1"
                    size="small"
                  ></el-input>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(model.marketAuthor)">复制</span>
                </div>
                <div>
                  <el-button type="primary" size="small" @click="addMarketAuthor" v-if="canAddMarketAuthor">
                      <i class="el-icon-plus"></i>
                  </el-button>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex">
          <!--生产厂家地址-->
          <el-col :md="8" :sm="24" :xs="24">
            <el-form-item
              prop="productionAddress"
              ref="productionAddress"
              :class="{
                'is-change': changeList.includes('productionAddress') || coorectionType['productionAddress'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['productionAddress']"
                  :disabled="!coorectionType['productionAddress']"
                  placement="top-start"
                >
                  <span>生产厂家地址</span>
                </el-tooltip>
              </template>
              <div class="copy-input-container">
                <div class="copy-input">
                  <el-input
                    v-model="model.productionAddress"
                    maxlength="500"
                    :disabled="(modelAttr.productionAddressDisabled || formDisable) && formAuth.indexOf('productionAddress') == -1"
                    size="small"
                  ></el-input>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton"  @click="copyInfo(model.productionAddress)">复制</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <!--上市许可持有人地址-->
          <el-col :md="8" :sm="24" :xs="24" v-show="colToggle([TYPEENUM.GENERAL_MEDICINE], 'marketAuthorAddress')">
            <el-form-item
              prop="marketAuthorAddress"
              ref="marketAuthorAddress"
              :class="{
                'is-change': changeList.includes('marketAuthorAddress') || coorectionType['marketAuthorAddress'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['marketAuthorAddress']"
                  :disabled="!coorectionType['marketAuthorAddress']"
                  placement="top-start"
                >
                  <span>上市许可持有人地址</span>
                </el-tooltip>
              </template>
              <div class="copy-input-container">
                <div class="copy-input">
                  <el-input
                    v-model="model.marketAuthorAddress"
                    maxlength="500"
                    :disabled="(modelAttr.marketAuthorAddressDisabled || formDisable) && formAuth.indexOf('marketAuthorAddress') == -1"
                    size="small"
                  ></el-input>
                </div>
                <div class="copy-btn">
                  <span class="copy-btn-below" v-if="shouldShowCopyButton" @click="copyInfo(model.marketAuthorAddress)">复制</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
		<el-row v-if="showBAR() || model.spuCategory == 3" type="flex">
			<el-col :md="8" :sm="24" :xs="24">
				<el-form-item
					:prop="model.spuCategory == 3 ? '' : 'filingsAuthor'"
					ref="filingsAuthor"
					:class="{
						'is-change': changeList.includes('filingsAuthor') || coorectionType['filingsAuthor'],
					}"
				>
				<template v-slot:label>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="coorectionType['filingsAuthor']"
                      :disabled="!coorectionType['filingsAuthor']"
                      placement="top-start"
                    >
                      <span>{{ model.spuCategory == 3 ? '医疗器械注册人/备案人名称' : '化妆品备案人/注册'}}</span>
                    </el-tooltip>
				</template>
				<el-input
					:value="model.filingsAuthor"
					maxlength="500"
					:disabled="modelAttr.filingsAuthorDisabled || formDisable"
					size="small"
					@input="(val) => { valueInput('model', 'filingsAuthor', val, /^.{0,50}$/) }"
				></el-input>
				</el-form-item>
			</el-col>
		</el-row>
        <el-row type="flex">
          <!--六级分类-->
          <el-col :md="16" :sm="24" :xs="24">
            <el-form-item label="六级分类" prop="sixCategory" ref="sixCategory" :class="{ 'is-change': changeList.includes('sixCategory') }">
              <el-popover placement="top-start" title="" width="300" trigger="hover" :content="popoverStr">
                <el-select
                  slot="reference"
                  v-model="model.sixCategory"
                  :disabled="(modelAttr.sixCategoryDisabled || formDisable) && formAuth.indexOf('sixCategory') == -1"
                  @change="sixCategoryChange"
                  placeholder="请输入六级分类名称"
                  :remote-method="debounce(searchCategory, 300)"
                  filterable
                  remote
                  :loading="modelAttr.sixCategoryLoad"
                  size="small"
                >
                  <el-option
                    v-for="item in modelAttr.sixCategoryOptions"
                    :key="'key_sixCategoryOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-popover>
            </el-form-item>
          </el-col>
          <!--分类编码-->
          <el-col :md="8" :sm="12" :xs="24" class="reset-search-button">
            <el-form-item prop="categoryCode" ref="categoryCode" v-change="model.categoryCode">
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['categoryCode']"
                  :disabled="!coorectionType['categoryCode']"
                  placement="top-start"
                >
                  <span>分类编码</span>
                </el-tooltip>
              </template>
              <el-input v-model="model.categoryCode" disabled size="small"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--税务分类编码-->
          <el-col :md="8" :sm="12" :xs="24" class="reset-link">
            <el-form-item
              label="税务分类编码"
              prop="taxCategoryCode"
              ref="taxCategoryCode"
              :class="{
                'is-change': changeList.includes('taxCategoryCode') || coorectionType['taxCategoryCode'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['taxCategoryCode']"
                  :disabled="!coorectionType['taxCategoryCode']"
                  placement="top-start"
                >
                  <span>税务分类编码</span>
                </el-tooltip>
              </template>
              <el-input
                type="number"
                v-model="model.taxCategoryCode"
                maxlength="16"
                :disabled="(modelAttr.taxCategoryCodeDisabled || formDisable) && formAuth.indexOf('taxCategoryCode') == -1"
                size="small"
                style="transform: translateY(5px)"
              >
                <template slot="append">
                  <el-link type="primary" slot="append" href="https://bmjc.jss.com.cn/Contents/smartCode/web.html" target="_blank">
                    税编网
                    <i class="el-icon-arrow-right el-icon--right"></i> </el-link
                ></template>
              </el-input>
            </el-form-item>
          </el-col>
          <!--进项税率-->
          <el-col :md="8" :sm="24" :xs="24">
            <el-form-item
              label="进项税率"
              prop="inRate"
              ref="inRate"
              :class="{
                'is-change': changeList.includes('inRate') || coorectionType['inRate'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['inRate']"
                  :disabled="!coorectionType['inRate']"
                  placement="top-start"
                >
                  <span>进项税率</span>
                </el-tooltip>
              </template>
              <el-radio-group
                v-model="model.inRate"
                :disabled="(modelAttr.inRateDisabled || formDisable) && formAuth.indexOf('inRate') == -1"
                size="small"
              >
                <el-radio
                  v-for="item in $store.getters.selectOptions.inRateOptions"
                  :key="'key_inRateOptions_' + item.id"
                  :label="item.id"
                  :disabled="!item.isValid"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!--销项税率-->
          <el-col :md="8" :sm="24" :xs="24">
            <el-form-item
              label="销项税率"
              prop="outRate"
              ref="outRate"
              :class="{
                'is-change': changeList.includes('outRate') || coorectionType['outRate'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['outRate']"
                  :disabled="!coorectionType['outRate']"
                  placement="top-start"
                >
                  <span>销项税率</span>
                </el-tooltip>
              </template>
              <el-radio-group
                v-model="model.outRate"
                :disabled="(modelAttr.outRateDisabled || formDisable) && formAuth.indexOf('outRate') == -1"
                size="small"
              >
                <el-radio
                  v-for="item in $store.getters.selectOptions.outRateOptions"
                  :key="'key_outRateOptions_' + item.id"
                  :label="item.id"
                  :disabled="!item.isValid"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--批件规格 -->
          <!-- TYPEENUM: {
            EMPTY: "", //未选择
            GIFT: 6, //赠品
            NOT_MEDICINE: 5, //非药
            MEDICAL_INSTRUMENT: 4, //医疗器械
            TRADITIONAL_MEDICINE: 3, //中药
            GENERAL_MEDICINE: 1 //普通药品
          }-->
          <el-col
            :md="8"
            :sm="24"
            :xs="24"
            v-show="
              colToggle(
                [TYPEENUM.EMPTY, TYPEENUM.NOT_MEDICINE, TYPEENUM.MEDICAL_INSTRUMENT, TYPEENUM.TRADITIONAL_MEDICINE, TYPEENUM.GENERAL_MEDICINE],
                'instructionSpec'
              )
            "
          >
            <el-form-item
              prop="instructionSpec"
              ref="instructionSpec"
              :class="{
                'is-change': changeList.includes('instructionSpec') || coorectionType['instructionSpec'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['instructionSpec']"
                  :disabled="!coorectionType['instructionSpec']"
                  placement="top-start"
                >
                  <span>批件规格</span>
                </el-tooltip>
              </template>
              <el-input
                v-model="model.instructionSpec"
                maxlength="300"
                :disabled="(modelAttr.instructionSpecDisabled || formDisable) && formAuth.indexOf('instructionSpec') == -1"
                size="small"
              >
                <el-popover
                  v-if="model.instructionSpec.length > 5"
                  placement="top"
                  width="400"
                  trigger="hover"
                  slot="append"
                  :content="model.instructionSpec"
                >
                  <i class="el-icon-more" slot="reference"></i>
                </el-popover>
              </el-input>
            </el-form-item>
          </el-col>
          <!-- 批件图片 -->
          <el-col :md="8" :sm="12" :xs="24">
            <el-form-item
              label="批件图片"
              prop="approvalImgList"
              ref="approvalImgList"
              :class="{
                'is-change': changeList.includes('approvalImgList') || coorectionType['approvalImgList'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['approvalImgList']"
                  :disabled="!coorectionType['approvalImgList']"
                  placement="top-start"
                >
                  <span>批件图片</span>
                </el-tooltip>
              </template>
              <el-form style="display: inline-block">
                <el-button @click="approvalPreview" size="small">预览（{{ model.approvalImgList.length }}）</el-button>
              </el-form>
              <el-button
                size="small"
                circle
                icon="el-icon-upload2"
                @click="modelAttr.approvalImgUpload = true"
                :disabled="modelAttr.approvalImgListDisabled || formDisable"
              ></el-button>
              <!-- <el-input v-model="model.approvalImgList.length" disabled class="approvalImgInput">
                <el-button type="primary" class="approvalImgBtn">预览</el-button>
                <el-button type="primary" class="approvalImgBtn">上传</el-button>
              </el-input>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <!--规格/型号-->
          <el-col :md="6" :sm="12" :xs="24" v-show="colToggle([TYPEENUM.GIFT], 'spec')">
            <el-form-item
              label="规格/型号"
              prop="spec"
              ref="spec"
              :class="{
                'is-change': changeList.includes('spec') || coorectionType['spec'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['spec']"
                  :disabled="!coorectionType['spec']"
                  placement="top-start"
                >
                  <span>规格/型号</span>
                </el-tooltip>
              </template>
              <el-input v-model="model.spec" :disabled="modelAttr.specDisabled" maxlength="40" size="small"></el-input>
            </el-form-item>
          </el-col>

          <!--是否委托生产-->
          <!-- TYPEENUM: {
            EMPTY: "", //未选择
            GIFT: 6, //赠品
            NOT_MEDICINE: 5, //非药
            MEDICAL_INSTRUMENT: 4, //医疗器械
            TRADITIONAL_MEDICINE: 3, //中药
            GENERAL_MEDICINE: 1 //普通药品
          }-->
          <el-col :md="6" :sm="12" :xs="24" v-show="colToggle([TYPEENUM.GIFT], 'delegationProduct')">
            <el-form-item
              prop="delegationProduct"
              ref="delegationProduct"
              :class="{
                'is-change': changeList.includes('delegationProduct') || coorectionType['delegationProduct'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['delegationProduct']"
                  :disabled="!coorectionType['delegationProduct']"
                  placement="top-start"
                >
                  <span>是否委托生产</span>
                </el-tooltip>
              </template>
              <el-radio-group v-model="model.delegationProduct" :disabled="modelAttr.delegationProductDisabled" size="small">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <!--委托生产厂家-->
          <el-col
            :md="6"
            :sm="12"
            :xs="24"
            style="position: relative"
            v-show="colToggle([TYPEENUM.GIFT], 'entrustedManufacturer') && model.delegationProduct === 1"
          >
            <el-form-item
              prop="entrustedManufacturer"
              ref="entrustedManufacturer"
              :class="{
                'is-change': changeList.includes('entrustedManufacturer') || coorectionType['entrustedManufacturer'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['entrustedManufacturer']"
                  :disabled="!coorectionType['entrustedManufacturer']"
                  placement="top-start"
                >
                  <span>受托生产厂家</span>
                </el-tooltip>
              </template>
              <el-select
                v-model="model.entrustedManufacturer"
                :filter-method="searchEntrustedManufacturer"
                :disabled="modelAttr.entrustedManufacturerDisabled"
                placeholder="请选择"
                ref="entrustedManufacturerSelect"
                filterable
                size="small"
              >
                <el-option
                  v-for="item in modelAttr.entrustedManufacturerOptions"
                  :key="'key_manufacturerOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!--有效期-->
          <el-col :md="6" :sm="12" :xs="24" v-show="colToggle([TYPEENUM.GIFT], 'validity,validityUnit')" class="reset-input-with-select">
            <el-form-item
              prop="validity"
              ref="validity"
              :class="{
                'is-change': changeList.includes('validity') || coorectionType['validity'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['validity']"
                  :disabled="!coorectionType['validity']"
                  placement="top-start"
                >
                  <span>有效期</span>
                </el-tooltip>
              </template>
              <el-input type="text" v-model="model.validity" :disabled="modelAttr.validityDisabled">
                <el-select v-model="model.validityUnit" :disabled="modelAttr.validityUnitDisabled" slot="append" size="small">
                  <el-option
                    v-for="item in modelAttr.validityUnitOptions"
                    :key="'key_validityUnitOptions_' + item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-input>
            </el-form-item>
          </el-col>

          <!--建议零售价-->
          <el-col :md="6" :sm="12" :xs="24" v-show="colToggle([TYPEENUM.GIFT], 'suggestedPrice')">
            <el-form-item
              prop="suggestedPrice"
              ref="suggestedPrice"
              :class="{
                'is-change': changeList.includes('suggestedPrice') || coorectionType['suggestedPrice'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['suggestedPrice']"
                  :disabled="!coorectionType['suggestedPrice']"
                  placement="top-start"
                >
                  <span>建议零售价</span>
                </el-tooltip>
              </template>
              <el-input type="number" v-model="model.suggestedPrice" :disabled="modelAttr.suggestedPriceDisabled" size="small"></el-input>
            </el-form-item>
          </el-col>
          <!--包装单位-->
          <el-col :md="6" :sm="12" :xs="24" v-show="colToggle([TYPEENUM.GIFT], 'packageUnit')">
            <el-form-item
              label="包装单位"
              prop="packageUnit"
              ref="packageUnit"
              :class="{
                'is-change': changeList.includes('packageUnit') || coorectionType['packageUnit'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['packageUnit']"
                  :disabled="!coorectionType['packageUnit']"
                  placement="top-start"
                >
                  <span>包装单位</span>
                </el-tooltip>
              </template>
              <el-select v-model="model.packageUnit" :disabled="modelAttr.packageUnitDisabled" placeholder="请选择" filterable size="small">
                <el-option
                  v-for="item in $store.getters.selectOptions.packageUnitOptions"
                  :key="'key_packageUnitOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!--品牌/商标-->
          <el-col :md="6" :sm="12" :xs="24" v-show="colToggle([TYPEENUM.GIFT], 'brand')">
            <el-form-item
              label="品牌/商标"
              prop="brand"
              ref="brand"
              :class="{
                'is-change': changeList.includes('brand') || coorectionType['brand'],
              }"
            >
              <template v-slot:label>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="coorectionType['brand']"
                  :disabled="!coorectionType['brand']"
                  placement="top-start"
                >
                  <span>品牌/商标</span>
                </el-tooltip>
              </template>
              <el-input v-model="model.brand" :disabled="modelAttr.brandDisabled" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-show="colToggle([TYPEENUM.GIFT], 'smallPackageCodeList')" class="packageCode-wrap">
            <div class="packageCode-title">包装条码</div>
            <div class="packageCode-content">
              <!--无小包装条码-->
              <el-col :span="24" class="reset-packageBarcode" v-show="colToggle([TYPEENUM.GIFT], 'noSmallPackageCode')">
                <el-form-item
                  label="无小包装条码"
                  prop="noSmallPackageCode"
                  :class="{
                    'is-change': changeList.includes('noSmallPackageCode'),
                  }"
                >
                  <el-radio-group v-model="model.noSmallPackageCode" :disabled="modelAttr.noSmallPackageCodeDisabled" size="small">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="0">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <!--小包装条码-->
              <el-col :span="24" v-if="colToggle([TYPEENUM.GIFT], 'smallPackageCodeList') && !model.noSmallPackageCode ? true : false">
                <el-form-item
                  label="小包装条码"
                  prop="smallPackageCodeList"
                  ref="smallPackageCodeList"
                  :class="{
                    'is-change': changeList.includes('smallPackageCodeList') || coorectionType['smallPackageCodeList'],
                  }"
                >
                  <template v-slot:label>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="coorectionType['smallPackageCodeList']"
                      :disabled="!coorectionType['smallPackageCodeList']"
                      placement="top-start"
                    >
                      <span>小包装条码</span>
                    </el-tooltip>
                  </template>
                  <multiple-input
                    v-model="model.smallPackageCodeList"
                    :min="true"
                    :disabled="modelAttr.smallPackageCodeListDisabled"
                  ></multiple-input>
                </el-form-item>
              </el-col>

              <!--中包装条码-->
              <el-col :span="24" v-show="colToggle([TYPEENUM.GIFT], 'mediumPackageCodeList')">
                <el-form-item
                  label="中包装条码"
                  prop="mediumPackageCodeList"
                  ref="mediumPackageCodeList"
                  :class="{
                    'is-change': changeList.includes('mediumPackageCodeList') || coorectionType['mediumPackageCodeList'],
                  }"
                >
                  <template v-slot:label>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="coorectionType['mediumPackageCodeList']"
                      :disabled="!coorectionType['mediumPackageCodeList']"
                      placement="top-start"
                    >
                      <span>中包装条码</span>
                    </el-tooltip>
                  </template>
                  <multiple-input v-model="model.mediumPackageCodeList" :disabled="modelAttr.mediumPackageCodeListDisabled"></multiple-input>
                </el-form-item>
              </el-col>

              <!--件包装条码-->
              <el-col :span="24" v-show="colToggle([TYPEENUM.GIFT], 'piecePackageCodeList')">
                <el-form-item
                  label="件包装条码"
                  prop="piecePackageCodeList"
                  ref="piecePackageCodeList"
                  :class="{
                    'is-change': changeList.includes('piecePackageCodeList') || coorectionType['piecePackageCodeList'],
                  }"
                >
                  <template v-slot:label>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="coorectionType['piecePackageCodeList']"
                      :disabled="!coorectionType['piecePackageCodeList']"
                      placement="top-start"
                    >
                      <span>件包装条码</span>
                    </el-tooltip>
                  </template>
                  <multiple-input v-model="model.piecePackageCodeList" :disabled="modelAttr.piecePackageCodeListDisabled"></multiple-input>
                </el-form-item>
              </el-col>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div
      v-show="!colToggle([TYPEENUM.GIFT], 'piecePackageCodeList')"
      class="partition-content"
      style="height: 20px; background: #f0f2f5; padding-left: -20px"
    ></div>
    <el-dialog
      title="批件图片"
      width="960px"
      :visible.sync="modelAttr.approvalImgUpload"
      :show-close="false"
      destroy-on-close
      @open="beforeApprovalImgUploadOpen"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="approvalImgBox">
        <div class="tip">*请上传宽度大于800px(含)的图片</div>
        <upload-img
          ref="approvalImg"
          :fileList="model.approvalImgList"
          :limit="30"
          :minWidth="800"
          :uploadUrl="ImageUploadUrl"
          @change="uploadSuccess"
        ></upload-img>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelApproval()">取 消</el-button>
        <el-button type="primary" @click="saveApproval()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- SPU 查询 -->
    <spu-drawer-table ref="spuDrawerTable" @saveSpu="saveSpu"></spu-drawer-table>
    <imagePreview :on-close="closeApprovalViewer" v-if="modelAttr.approvalImgPreview" :url-list="model.approvalImgList"></imagePreview>
    <addManufacturer ref="addManufacturer" @saveManufacturer="saveManufacturer"></addManufacturer>
    <addMarketAuthor ref="addMarketAuthor" @saveMarketAuthor="saveMarketAuthor"></addMarketAuthor>
  </div>
</template>

<script>
import addMarketAuthor from "./components/addDict.vue"
import addManufacturer from "./components/addDict.vue"
import multipleInput from "@/components/common/multipleInput"
import spuDrawerTable from "./spu/components/spuDrawerTable"
import { getSpuList, getApprovalNoReg, getDictList } from "@/api/product"
import { dictSearchTypeAndName, categoryList, getTotalDictionaryTree, searchSixCategory } from "@/api/dict"
import { dataModel, dataModelAttr, dataRules, spuDrawerForm, validateChronicDiseasesVariety } from "@/utils/basicInfoData"
import uploadImg from "@/components/uploadImg/index"
import imagePreview from "@/components/common/preview/imagePreview"
import {
  setMaxLengthByByte,
  validityTransition,
  findIdByOptions,
  parseTimestamp,
  findNameByOptions,
  debounce,
  getId,
  isRepeat,
} from "@/utils/index.js"
import pinTo from "@/utils/pinTo.js"
const _defaultDataModel = _.cloneDeep(dataModel)
let businessScopeListOptionsParentId = []
export default {
  name: "basicInfo",
  components: {
    multipleInput,
    uploadImg,
    imagePreview,
    spuDrawerTable,
    addManufacturer,
    addMarketAuthor,
  },
  props: {
    // 表单编辑权限
    formAuth: {
      type: Array,
      required: false,
      default: () => [],
    },
    formDisable: {
      type: Boolean,
      default: false,
    },
    /**
     * spu渲染数据
     */
    spuData: {
      type: Object,
      required: false,
      default: () => {},
    },
    /**
     * sku渲染数据 (赠品使用)
     */
    skuData: {
      type: Array,
      required: false,
      default: () => [],
    },
    /**
     * sau渲染数据
     */
    sauData: {
      type: Array,
      required: false,
      default: () => [],
    },
    // 商品修改改变字段
    changeList: {
      type: Array,
      required: false,
      default: () => [],
    },
    coorection: {
      type: Object,
      required: false,
      default: () => {},
    },
  },
  data() {
    return {
      showForm: true,
      popoverStr: "", //六级分类悬浮文案
      zyypID: -1,
      // 化妆品id
      hzpID: -1,
      // 所属经营范围保健食品id
      bjpID: 0,
      // 处方药id
      cfyId: 0,
      // 批准文号 label 文本
      approvalNoLabel: "批准文号",
      // 编辑权限:不可以编辑项
      editPermission: [],
      // spu 复用表格加载状态
      spuDrawerTableLoading: false,
      // 是否展示SPU复用
      spuDrawer: false,
      // spu组件加载状态
      spuLoading: true,
      //商品分类类型枚举对象,
      TYPEENUM: {
        EMPTY: "", //未选择
        GIFT: 5, //赠品
        NOT_MEDICINE: 4, //非药
        MEDICAL_INSTRUMENT: 3, //医疗器械
        TRADITIONAL_MEDICINE: 2, //中药
        GENERAL_MEDICINE: 1, //普通药品 1
      },

      //model对象
      model: dataModel,

      // 跟 model对象对应，增加选项，展示控制等其他属性
      modelAttr: dataModelAttr,

      //校验规则
      rules: dataRules,
      ImageUploadUrl: process.env.VUE_APP_BASE_API + "/api/file/upload/certificateImage",
      // spu复用查询表单
      spuDrawerForm,
      //  spu复用弹层选项
      spuDrawerFormOptions: {
        //二级分类
        secondCategoryDisabled: true,
        secondCategoryOptions: [],
        secondCategoryLoad: false,

        //三级分类
        thirdCategoryDisabled: true,
        thirdCategoryOptions: [],
        thirdCategoryLoad: false,

        fourthCategoryDisabled: true,
        fourthCategoryOptions: [],
        fourthCategoryLoad: false,

        fiveCategoryDisabled: true,
        fiveCategoryOptions: [],
        fiveCategoryLoad: false,

        sixCategoryDisabled: true,
        sixCategoryOptions: [],
        sixCategoryLoad: false,

        // 生产厂家
        manufacturerOptions: this.$store.getters.selectOptions.distManufacturerOptions,
      },
      spuDrawerTableData: [],
      showApprovalNoQue: false, //控制批准文号字段输入提示显示
      regInfo: {},
      limitedCNMedicines: [],  //中药限品种
      CNRangeName: "", //中药
      CNNoLimitedRangeName: '', //中药饮片
      CNLimitedRangeName: '', //中药饮片(限品种)
      disinfectantProductsRange: '', //消毒产品范围
      otherRange: '', //其它范围
      foodRange: '', //食品范围
      cosmeticsRange: '', //化妆品范围
      stopAutoFillInfo: false, //是否禁止自动填充信息
      resetKey: 0,
      marketAuthorDict: [],
    }
  },
  computed: {
    /**
     * @description: 是否展示表单列,使用计算属性是为了触发动态的数据更新
     * @param {TYPEENUM} spuCategoryAry
     * @param {string} modelField
     * @return:
     */
    colToggle(spuCategoryAry, modelField) {
      // 根据商品类型检测是否显示当前列
      return this.checkStateByspuCategory(spuCategoryAry, modelField)
    },
    prescriptionCategoryOptions() {
      return this.$store.getters.selectOptions.prescriptionCategoryOptions
    },
    // 商品操作类型
    operationType: function () {
      return this.$store.getters.operationType
    },
    skuForm: function () {
      return this.$store.state.product.skuForm
    },
    // 生产厂家默认提示文本
    manufacturerPlaceholder: function () {
      // 同步待提交 && 接口数据存在生产厂家文本
      if (this.operationType == "update" && this.spuData.manufacturer) {
        return this.spuData.manufacturer
      }
      return "请选择"
    },
    coorectionType: function () {
      return this.coorection ? this.coorection : {}
    },
    isCoorection: function () {
      return this.$route.query.type == "coorection"
    },
    // 新增：复制按钮显示条件
    shouldShowCopyButton() {
      // 主要条件：operationType为detail或edit
      if (this.operationType === 'detail' || this.operationType === 'edit') {
        return true;
      }
      
      // 备用条件：根据URL参数判断
      const urlParam = this.$route.query;
      
      // 如果是纠错类型或上架类型，显示复制按钮（这些通常是detail模式）
      if (urlParam.type === 'coorection' || urlParam.type === 'shelves') {
        return true;
      }

      if(urlParam.procKey === '新品上报流程'){
        return true;
      }
      
      // 如果URL中没有明确的新增标识，且有productCode，可能是编辑或详情模式
      if (urlParam.productCode && !urlParam.isAdd) {
        return true;
      }
      
      return false;
    },
    //新增生产厂家按钮显示条件
    canAddManufacturer(){
      // 主要条件：operationType为add或edit
      if (this.operationType === 'add' || this.operationType === 'edit') {
        return true;
      }
      
      // 备用条件：根据URL参数判断
      const urlParam = this.$route.query;
      
      // 如果是纠错类型或上架类型，显示复制按钮（这些通常是detail模式）
      if (urlParam.type === 'present' || urlParam.type === 'update') {
        return true;
      }
      
      return false;
    },
    canAddMarketAuthor(){
      // 主要条件：operationType为add或edit
      if (this.operationType === 'add' || this.operationType === 'edit') {
        return true;
      }
      
      // 备用条件：根据URL参数判断
      const urlParam = this.$route.query;
      
      // 如果是纠错类型或上架类型，显示复制按钮（这些通常是detail模式）
      if (urlParam.type === 'present' || urlParam.type === 'update') {
        return true;
      }
      
      return false;
    },
  },
  watch: {
    prescriptionCategoryOptions(val) {
      val.forEach((item) => {
        if (item.dictName == "处方药") {
          this.cfyId = item.id
          return
        }
      })
    },
    skuForm: {
      handler: function (val) {
        this.filterChronicDiseasesVariety(val.prescriptionCategory, this.model.sixCategory)
      },
      deep: true,
    },
    spuData: {
      handler: async function (newValue, oldValue) {
        this.spuLoading = true
        await this.checkDistState()
        if(newValue.spuCategory === this.TYPEENUM.TRADITIONAL_MEDICINE || this.model.spuCategory === this.TYPEENUM.GENERAL_MEDICINE) {
          this.selectTargetFirstClass(newValue.firstCategoryName + " >")
        }
        // 纠错修改时触发，此时不再执行 fillDataModel，直接执行 checkEditPermission 触发编辑权限逻辑
        if (newValue._coorectionChange) {
          // 如果存在编辑权限备份副本，先还原
          if (this._modelAttr) {
            this.modelAttr = this._modelAttr
          }
          this.checkEditPermission()
        } else {
          if (newValue !== oldValue) {
            this.fillDataModel()
          }
        }
      },
      deep: true,
    },
    // 检测form值变动，emit 出表单的改变状态
    model: {
      handler: function (newValue, oldValue) {
        if (!this.spuLoading) {
          this.diffSpuModel(newValue)
        }
      },
      deep: true,
    },
    // 批准文号
    "model.approvalNo": function (val) {
      if (!this.spuLoading) {
        this.setTaxCategoryCodeFunc(val)
      }
      if(this.$route.query.pageType != 'detail' && this.$route.query.pageType != 'edit'){
        this.$store.commit("product/SET_SPU_APPROVAL_NO", this.model.approvalNo)
      }
    },
    // 是否委托生产
    "model.delegationProduct": function (val) {
      if (val === 1) {
        this.rules.entrustedManufacturer[0].required = true
      } else {
        this.rules.entrustedManufacturer[0].required = false
        this.model.entrustedManufacturer = ""
        this.model.entrustedManufacturerName = ""
      }
    },
    "model.businessScopeList": function (val, oldValue) {
      // console.log(JSON.stringify(val))
      if (!this.spuLoading) {
        let arr = _.cloneDeep(val)
        if (val.length > 20) {
          arr.splice(20, 1)
          this.model.businessScopeList = arr
        } else {
        }
      }
    },
    // 设置字节长度
    "model.spec": function (val) {
      if (val) {
        this.model.spec = setMaxLengthByByte(val, 40)
      }
    },
    // 有效期单位转换, 规则：-1 对应 - ;0 对应 *;
    "model.validity": function (val, oldValue) {
      if (/^(-|\*)?$|^[1-9]{1}\d{0,3}$/g.test(val)) {
        switch (val) {
          case "-":
            this.model.validityTransition = -1
            break
          case "*":
            this.model.validityTransition = 0
            break
          default:
            this.model.validityTransition = val
            break
        }
      } else {
        this.model.validity = oldValue
      }
    },
    "model.generalName": function (value) {
      // debugger
      if (this._generalNameHandle) {
        this._generalNameHandle()
      }
      if(this.$route.query.pageType != 'detail' && this.$route.query.pageType != 'edit' && this.stopAutoFillInfo === false){
        this.$bus.$emit('reMatchByGaeneralName', this.model.generalName)
        this.$store.commit('product/SET_GENERAL_NAME', this.model.generalName)
        this.changeGeneralName()
        this.autoFillInfo(this.model.spuCategory, '', 'NO_CHANGE_CATEGORY')
      }else {
        this.$nextTick(() => {
          this.stopAutoFillInfo = false
        })
      }
    },
    // 生产厂家值变更
    "model.manufacturer": function (value, oldvalue) {
      // 生产厂家与上市许可持有人关联相关逻辑
      if (this.operationType == "add" || this.operationType == "update" || this.operationType == "present") {
        let manufacturerStr = findNameByOptions(value, "id", this.modelAttr.manufacturerOptions)
        this._oldManufacturerStr = this._oldManufacturerStr ? this._oldManufacturerStr : ""
        if (this.$store.getters.spuCategory.type == "GENERAL_MEDICINE" && this.model.marketAuthor == this._oldManufacturerStr) {
          this.model.marketAuthor = manufacturerStr
          this._oldManufacturerStr = manufacturerStr
        } else {
          // 不是普通药品时暂存生产厂家的值，方便当商品分类更改为普通药品时，赋值生产厂家值到上市许可持有人
          this._marketAuthor = manufacturerStr
        }
      }
      // 设置厂家分类选项
      for (let item of this.modelAttr.manufacturerOptions) {
        if (item.id == value) {
          this.model.manufacturerCategoryName = item.manufacturerCategoryName
        }
      }
    },
    // 无小包装条码逻辑处理;
    "model.noSmallPackageCode": function (val, oldValue) {
      if (val) {
        this.model.smallPackageCodeList = []
      }
    },
    /**
     * 商品分类改变
     */
    "model.spuCategory": async function (val) {
      // TYPEENUM: {
      //   EMPTY: "", //未选择
      //   GIFT: 6, //赠品
      //   NOT_MEDICINE: 5, //非药
      //   MEDICAL_INSTRUMENT: 4, //医疗器械
      //   TRADITIONAL_MEDICINE: 3, //中药
      //   GENERAL_MEDICINE: 1 //普通药品
      // },
      // 重置；
      // 根据商品大类&所属范围设置批准文号标题以及批准文号验证规则
      this.filterApprovalNo()
      if(val !== this.TYPEENUM.TRADITIONAL_MEDICINE) {
        this.$store.commit("product/SET_UNIT_TARGET_CATEGORY", false)
      }
      for (let item in this.rules) {
        this.rules[item][0].required = true
      }
      // 获取商品分类中文名称
      let name = findNameByOptions(val, "id", this.$store.getters.selectOptions.spuCategoryOptions)
      console.log(name,"qiyu")
      switch (val) {
        case this.TYPEENUM.EMPTY:
          break
        case this.TYPEENUM.GIFT: // 赠品
          let giftArr = [
            "approvalNo",
            // "storageCond",
            // "largeCategory",
            // "standardCodeList",
            "instructionSpec",
            "whetherSupervision",
            "dosageForm",
            "marketAuthor",
            "marketAuthorAddress",
            "shadingAttr",
          ]
          //赠品委托生产厂家校验处理（回显时）
          if (this.skuData[0] && this.skuData[0].delegationProduct !== 1) {
            giftArr.push("entrustedManufacturer")
          }
          // 商品分类为 赠品时不校验以下字段
          this.rulesChange(giftArr, false)
          this.$store.commit("product/SET_SPU_CATEGOTY", {
            type: "GIFT",
            name,
            id: val,
          })
          break
        case this.TYPEENUM.TRADITIONAL_MEDICINE: // 中药
          // 不校验以下字段
          // 品牌商标 brand;
          // spec 规格型号（赠品）;
          // packageUnit 包装单位
          this.rulesChange(
            [
              "brand",
              "spec",
              "approvalNo",
              "packageUnit",
              // "standardCodeList",
              "delegationProduct",
              "marketAuthor",
              "marketAuthorAddress",
              "entrustedManufacturer",
            ],
            false
          )
          this.$store.commit("product/SET_SPU_CATEGOTY", {
            type: "TRADITIONAL_MEDICINE",
            name,
            id: val,
          })
          break
        case this.TYPEENUM.NOT_MEDICINE: // 非药
          // 商品分类为非药时不校验以下字段：
          // 品牌商标 brand;
          // spec 规格型号（赠品）;
          // packageUnit 包装单位
          this.$emit("isShowBtn", this.model.businessScopeList, this.model.spuCategory, this.bjpID)
          this.rulesChange(
            [
              "brand",
              "spec",
              "packageUnit",
              // "standardCodeList",
              "delegationProduct",
              "marketAuthor",
              "marketAuthorAddress",
              "entrustedManufacturer",
            ],
            false
          )
          this.$store.commit("product/SET_SPU_CATEGOTY", {
            type: "NOT_MEDICINE",
            name,
            id: val,
          })
          break
        case this.TYPEENUM.MEDICAL_INSTRUMENT: // 医疗器械
          // 商品分类为医疗器械时不校验以下字段：
          // 品牌商标 brand;
          // spec 规格型号（赠品）;
          // packageUnit 包装单位
          this.rulesChange(
            [
              "brand",
              "spec",
              "packageUnit",
              // "standardCodeList",
              "delegationProduct",
              "marketAuthor",
              "marketAuthorAddress",
              "entrustedManufacturer",
            ],
            false
          )
          this.$store.commit("product/SET_SPU_CATEGOTY", {
            type: "MEDICAL_INSTRUMENT",
            name,
            id: val,
          })
          break
        default:
          //普通药品
          // 商品分类为普通时不校验以下字段：
          // 品牌商标 brand;
          // spec 规格型号（赠品）;
          // packageUnit 包装单位
          this.rulesChange(["brand", "spec", "packageUnit", "delegationProduct", "entrustedManufacturer"], false)
          // 普通药品校验：
          // standardCodeList : 本位码
          this.rulesChange(["marketAuthor", "marketAuthorAddress"], true)
          this.$store.commit("product/SET_SPU_CATEGOTY", {
            type: "GENERAL_MEDICINE",
            name,
            id: val,
          })
          break
      }
      // 商品分类和六级分类关联交互处理
      this.modelAttr.sixCategoryDisabled = false
      // 商品分类和所属经营范围调整
      this.getbusinessScopeListOptions(val)
      //重置表单
      this.resetForm(val)
    },
    "model.businessScopeList": function (val) {
      // 根据商品大类&所属范围设置批准文号标题以及批准文号验证规则
      this.filterApprovalNo()
    },
    /**
     * 六级分类选择
     */
    "model.sixCategory": function (val) {
      if (val) {
        // this.setCategoryVal(val)
        // 根据六级分类设置一到五级ID
        let categoryArr = ["", "firstCategory", "secondCategory", "thirdCategory", "fourthCategory", "fiveCategory"]
        for (let item of this.modelAttr.sixCategoryOptions) {
          if (item.id == val) {
            for (let category of item.childrenNodes) {
              this.model[categoryArr[category.levelNode]] = category.id
            }
          }
        }
        this.$store.commit("product/SET_FIRSTCATEGORY", this.model.firstCategory)
      }
    },
    /**
     * 一级分类选择 (复用查询)
     */
    "spuDrawerForm.firstCategory": function (val) {
      this.getDrugLevelOptions("secondCategory", val, true)
      this.spuDrawerFormOptions.thirdCategoryDisabled = true
    },
    /**
     * 二级分类选择 (复用查询)
     */
    "spuDrawerForm.secondCategory": function (val) {
      if (val) {
        this.spuDrawerForm.thirdCategory = ""
        this.spuDrawerForm.fourthCategory = ""
        this.spuDrawerForm.fiveCategory = ""
        this.spuDrawerForm.sixCategory = ""
        this.getDrugLevelOptions("thirdCategory", val, true)
        this.spuDrawerFormOptions.thirdCategoryDisabled = false
      }
    },
    /**
     * 三级分类选择 (复用查询)
     */
    "spuDrawerForm.thirdCategory": function (val) {
      if (val) {
        this.spuDrawerForm.fourthCategory = ""
        this.spuDrawerForm.fiveCategory = ""
        this.spuDrawerForm.sixCategory = ""
        this.getDrugLevelOptions("fourthCategory", val, true)
        this.spuDrawerFormOptions.fourthCategoryDisabled = false
      }
    },
    /**
     * 四级分类选择 (复用查询)
     */
    "spuDrawerForm.fourthCategory": function (val) {
      if (val) {
        this.spuDrawerForm.fiveCategory = ""
        this.spuDrawerForm.sixCategory = ""
        this.getDrugLevelOptions("fiveCategory", val, true)
        this.spuDrawerFormOptions.fiveCategoryDisabled = false
      }
    },
    /**
     * 五级分类选择 (复用查询)
     */
    "spuDrawerForm.fiveCategory": function (val) {
      if (val) {
        this.spuDrawerForm.sixCategory = ""
        this.getDrugLevelOptions("sixCategory", val, true)
        this.spuDrawerFormOptions.sixCategoryDisabled = false
      }
    },

  },
  async created() {
    const regInfo = await getApprovalNoReg()
    this.regInfo = regInfo.data
    this.init()
    await this.getLimitedCNMedicines()
  },
  mounted() {

  },
  methods: {
    clearManufacturer(){
      this.$set(this.model,'productionAddress', '')
    },
    async saveMarketAuthor(){
      await this.getMarketAuthorDict()
      const row = this.marketAuthorDict.find(item => item.dictName === this.model.marketAuthor)
      this.$nextTick(() => {
        if(row){
          this.$set(this.model, 'marketAuthor', row.marketAuthor)
          this.$set(this.model,'marketAuthorAddress', row.marketAuthorAddress)
        }
      })
    },
    async saveManufacturer(productAdress, manufacturerName){
      await this.searchManufacturer(manufacturerName)
      this.$nextTick(() => {
        this.$set(this.model, 'manufacturerName', manufacturerName)
        this.$set(this.model, 'productionAddress', productAdress)
        this.manufacturerChange(this.model.manufacturerName, 'handel')
      })
    },
    copyInfo(info){
      if(info){
        if(navigator.clipboard && navigator.clipboard.writeText){
          navigator.clipboard.writeText(info).then(() => {
            this.$message.success('复制成功')
          })
        }else {
          const textArea = document.createElement('textarea');
          textArea.value = info;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.$message.success('复制成功')
        }
      }
    },
    async changeGeneralName(){
      if(this.model.generalName && this.$route.query.pageType != 'detail'){
        await this.searchCategory(this.model.generalName)
        if(this.modelAttr.sixCategoryOptions.length == 1){
          this.$set(this.model, "sixCategory", this.modelAttr.sixCategoryOptions[0].id)
          this.sixCategoryChange(this.modelAttr.sixCategoryOptions[0].id)
        }else {
          this.$set(this.model, "sixCategory", "")
        }
      }
      if(this.$route.query.pageType != 'detail'){
        this.autoFillInfo(this.model.spuCategory, '', 'NO_CHANGE_CATEGORY')
        this.$store.commit('product/SET_GENERAL_NAME', this.model.generalName)
      }
    },
    async getLimitedCNMedicines(){
      const res = await getDictList(27)
      this.limitedCNMedicines = res
    },
    approvalNoBlur(){
      if(this.$route.query.pageType != 'detail'){
        this.autoFillInfo(this.model.spuCategory, 'approvalNoBlur', 'NO_CHANGE_CATEGORY')
        this.$store.commit("product/SET_SPU_APPROVAL_NO", this.model.approvalNo)
      }
    },
    autoFillInfo(e, from, isChangeCategory){
      // console.log('====================================');
      // console.log(e);
      // console.log(this.$store.getters.selectOptions.dosageFormOptions);
      // console.log('所属经营范围', this.modelAttr.businessScopeListOptions, "值", this.model.businessScopeList);
      // console.log('====================================');
      
      this.$store.commit('product/SET_IS_CHANGE_CATEGORY', String(isChangeCategory))
      if(e == 2){
        if(!from == 'approvalNoBlur'){
          this.$set(this.model, 'approvalNo', '-')
        }
        this.$set(this.model, 'shadingAttr', 6)
        const dosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '无') || ''
        this.$set(this.model, "dosageForm", dosageForm.id)
        this.$set(this.model, "instructionSpec", '无')
        const dictNames = this.limitedCNMedicines.map(item => item.dictName)
        if(dictNames.includes(this.model.generalName)){
          const randomCode = Math.floor(Math.random() * 10) + 1
          this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `limited_cn_medicines_${randomCode}`)
          this.$nextTick(() => {
            this.model.businessScopeList = []
            this.model.businessScopeList.push([this.CNRangeName.id, this.CNLimitedRangeName.id])
            this.resetKey++
          })
        }else {
          const randomCode = Math.floor(Math.random() * 10) + 1
          this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `non_limited_cn_medicines_${randomCode}`)
          this.$nextTick(() => {
            this.model.businessScopeList = []
            this.model.businessScopeList.push([this.CNRangeName.id, this.CNNoLimitedRangeName.id])
            this.resetKey++
          })
        }
      }else if(e == 3){
        this.$set(this.model, 'shadingAttr', 6)
        const targetDosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '其他') || ''
        this.$set(this.model, "dosageForm", targetDosageForm.id)
      }else if(e == 4){
        if(this.model.approvalNo.includes('妆')){
          this.$set(this.model, 'shadingAttr', 6)
          const targetDosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '其他') || ''
          this.$set(this.model, "dosageForm", targetDosageForm.id)
          this.$set(this.model, "instructionSpec", '无')
          this.$nextTick(() => {
            this.model.businessScopeList = []
            this.model.businessScopeList.push([this.cosmeticsRange.id])
            this.resetKey++
          })
        }else if (this.model.businessScopeList.length > 0 && this.model.businessScopeList){
          const foodRangeTarget = this.model.businessScopeList.find(item => item.includes(this.foodRange.id))
          if(foodRangeTarget){
            const foodTargetA = this.foodRange.children.find(item => item.dictName == '其他婴幼儿配方食品')
            const foodTargetB = this.foodRange.children.find(item => item.dictName == '婴幼儿配方乳粉')
            const foodTargetC = this.foodRange.children.find(item => item.dictName == '特殊医学用途配方食品')
            const foodTargetD = this.foodRange.children.find(item => item.dictName == '预包装食品')
            const foodTargetE = this.foodRange.children.find(item => item.dictName == '保健食品')
            if (foodRangeTarget.includes(foodTargetA.id) || 
                  foodRangeTarget.includes(foodTargetB.id) || 
                  foodRangeTarget.includes(foodTargetC.id) ||
                  foodRangeTarget.includes(foodTargetD.id)){
                    const randomCode = Math.floor(Math.random() * 10) + 1
                    this.$set(this.model, 'shadingAttr', 4)
                    const targetDosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '其他') || ''
                    this.$set(this.model, "dosageForm", targetDosageForm.id)
                    if(foodRangeTarget.includes(foodTargetA.id)){
                      this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `non_health_food_A_${randomCode}`)
                    }else if (foodRangeTarget.includes(foodTargetB.id)){
                      this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `non_health_food_B_${randomCode}`)
                    }else if (foodRangeTarget.includes(foodTargetC.id)){ 
                      this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `non_health_food_C_${randomCode}`)
                    }else if(foodRangeTarget.includes(foodTargetD.id)){
                      this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `non_health_food_D_${randomCode}`)
                    }
                  }else if(foodRangeTarget.includes(foodTargetE.id)){
                    const randomCode = Math.floor(Math.random() * 10) + 1
                    this.$set(this.model, 'shadingAttr', 5)
                    const targetDosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '其他') || ''
                    this.$set(this.model, "dosageForm", targetDosageForm.id)
                    this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `health_food_${randomCode}`)
                  } 
          }
          const ortherRangeTarget = this.model.businessScopeList.find(item => item.includes(this.otherRange.id))
          if(ortherRangeTarget){
            const randomCode = Math.floor(Math.random() * 10) + 1
            this.$set(this.model, 'shadingAttr', 6)
            const targetDosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '其他') || ''
            this.$set(this.model, "dosageForm", targetDosageForm.id)
            this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `orther_range_${randomCode}`)
            
          }
          const disinfectantProductsRangeTarget = this.model.businessScopeList.find(item => item.includes(this.disinfectantProductsRange.id))
          if(disinfectantProductsRangeTarget){
            const randomCode = Math.floor(Math.random() * 10) + 1
            this.$set(this.model, 'shadingAttr', 6)
            const targetDosageForm = this.$store.getters.selectOptions.dosageFormOptions.find(item => item.dictName == '其他') || ''
            this.$set(this.model, "dosageForm", targetDosageForm.id)
            this.$store.commit('product/SET_SPU_BUSINESS_SCOPE_LIST_TYPE', `disinfectant_products_${randomCode}`)
          }
        }
      }
    },
    async getMarketAuthorDict(){
      const res = await getDictList(28)
      this.marketAuthorDict = res
    },
    async addMarketAuthor(){
      await this.getMarketAuthorDict()
      const row = this.marketAuthorDict.find(item => item.dictName === this.model.marketAuthor) || {}
      this.$refs.addMarketAuthor.open({
        title: '新增上市许可持有人',
        dictName: this.model.marketAuthor,
        row: row,
        type: 28,
      })
    },
    addManufacturer(){
      // console.log('====================================');
      // console.log(this.model.manufacturerName, '新增生产厂家');
      // console.log('====================================');
      const row = this.modelAttr.manufacturerOptions.find(item => item.dictName === this.model.manufacturerName) || {}
      this.$refs.addManufacturer.open({
        title: '新增生产厂家',
        dictName: this.model.manufacturerName,
        row: row,
        type: 12,
      })
    },
    showBAR(){
      if(process.env.NODE_ENV === 'test'){
        return this.model.businessScopeList.some(item => item.includes(21439))
      }else{
        return this.model.businessScopeList.some(item => item.includes(1154))
      }
    },
    hasShadingAttr(){
      if(this.operationType === 'present'){
        return ''
      }else{
        return 'shadingAttr'
      }
    },
    // 修改商品大类联动批准文号
    changeSpuCategory(e) {
      this.model.businessScopeList = []
      if (e === 1 || e === 3 || (e === 4 && !this.modelAttr.approvalNoDisabled)) {
        this.model.approvalNo = this.initializedSpuModel.approvalNo
      } else if (e === 2) {
        this.model.approvalNo = "-"
      } else {
        this.model.approvalNo = ""
      }
      if(this.$route.query.pageType != 'detail'){
        this.autoFillInfo(e, '', 'CHANGE_CATEGORY')
      }
    },
    setTaxCategoryCodeFunc(val) {
      if (val.includes("国药准字S")) {
        this.setRate("3%")
        this.modelAttr.inRateDisabled = true
        this.modelAttr.outRateDisabled = true
      } else {
        this.modelAttr.inRateDisabled = false
        this.modelAttr.outRateDisabled = false
      }
    },

    editRateFunction() {
      // this.autoFillInfo(this.model.spuCategory)
      // this.$store.commit('product/SET_GENERAL_NAME', this.model.generalName)
      for (let index = 0; index < this.modelAttr.sixCategoryOptions.length; index++) {
        const element = this.modelAttr.sixCategoryOptions[index]
        if (element.id === this.model.sixCategory) {
          if (element.childrenNodes[0].id === 100010 && element.childrenNodes[1].id === 100127) {
            this.setRate("13%")
            this.modelAttr.inRateDisabled = false
            this.modelAttr.outRateDisabled = false
            this.model.taxCategoryCode = "*********"
            return
          }
          if (element.childrenNodes[0].id === 100010 && element.childrenNodes[1].id === 100133) {
            this.setRate("13%")
            this.modelAttr.inRateDisabled = false
            this.modelAttr.outRateDisabled = false
            this.model.taxCategoryCode = "*********"
            return
          }
        }
      }

      if (this.model.approvalNo.includes("国药准字H")) {
        this.setRate("13%")
        this.modelAttr.inRateDisabled = false
        this.modelAttr.outRateDisabled = false
        //若批准文号包含“国药准字H”，且通用名包含“控释片”或“缓释片”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        if (this.model.generalName.includes("控释片") || this.model.generalName.includes("缓释片")) {
          this.model.taxCategoryCode = "*********"
          return
        }

        //若批准文号包含“国药准字H”，且通用名不包含“控释片”或“缓释片”，但通用名以“片”结尾，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        if (!this.model.generalName.includes("控释片") && !this.model.generalName.includes("缓释片") && this.model.generalName.includes("片")) {
          this.model.taxCategoryCode = "*********"
          return
        }
        //若批准文号包含“国药准字H”，且剂型为“胶囊剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字H”，且剂型为“颗粒剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字H”，且剂型为“滴剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字H”，且剂型为“栓剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字H”，且剂型为“气雾剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字H”，且剂型为“口服液体剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        let codeObj = { 1011: "*********", 1013: "*********", 1074: "*********", 1027: "*********", 1056: "*********", 1015: "*********" }
        let key = this.model.dosageForm
        let value = codeObj[key]
        if (value) {
          this.model.taxCategoryCode = value
          return
        }
      } else if (this.model.approvalNo.includes("国药准字Z")) {
        this.setRate("13%")
        this.modelAttr.inRateDisabled = false
        this.modelAttr.outRateDisabled = false

        if (this.model.businessScopeList) {
          for (let index = 0; index < this.model.businessScopeList.length; index++) {
            const element = this.model.businessScopeList[index]
            if (element.indexOf(1149) !== -1) {
              this.model.taxCategoryCode = "*********"
              return
            }
          }
        }

        //若批准文号包含“国药准字Z”，且剂型为“丸剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“片剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“颗粒剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“糖浆剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“合剂”或“口服液”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“注射剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“胶囊剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“散剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“栓剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“酒剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        //若批准文号包含“国药准字Z”，且剂型为“油剂”，则“税务分类编码”自动填充为“*********”，“进项税率”、“销项税率”自动填充为13%，可修改。
        let codeObj = {
          1014: "*********",
          1010: "*********",
          1013: "*********",
          1018: "*********",
          1045: "*********",
          1017: "*********",
          1078: "*********",
          1011: "*********",
          1025: "*********",
          1027: "*********",
          1051: "*********",
          1075: "*********",
        }
        let key = this.model.dosageForm
        let value = codeObj[key]
        if (value) {
          this.model.taxCategoryCode = value
          return
        }
      }
    },
    /**
     *加载初始化数据
     */
    async init() {
      // 备份 spu form 数据，方便后期使用
      this._model = _.cloneDeep(this.model)
      // 生产厂家初始数据
      this.modelAttr.manufacturerOptions = this.modelAttr.entrustedManufacturerOptions =
        this.$store.getters.selectOptions.distManufacturerOptions
      // 初始化通用名和扩展属性处理逻辑
      this.generalNameHandle()
      // 接收审核信息
      this.$bus.$on("auditData", (data) => {
        this.auditData = data
      })
      // 有缓存时先渲染再更新选项数据
      if (this.cacheDist()) {
        if (this.operationType == "add") {
          this.fillDataModel()
        }
        await this.getDist()
      } else {
        await this.getDist()
        if (this.operationType == "add") {
          this.fillDataModel()
        }
      }
      if(this.model.generalName && this.$route.query.pageType != 'detail'){
        await this.searchCategory(this.model.generalName)
        if(this.modelAttr.sixCategoryOptions.length == 1){
          this.$set(this.model, "sixCategory", this.modelAttr.sixCategoryOptions[0].id)
          this.sixCategoryChange(this.modelAttr.sixCategoryOptions[0].id)
        }else{
          this.$set(this.model, "sixCategory", "")
        }
        this.$nextTick(() => {
          this.$store.commit('product/SET_GENERAL_NAME', this.model.generalName)
        })
      }
    },
    /**
     * @description: 检测 dist 选项是否已经加载完成
     * @return: promise
     */
    checkDistState() {
      let timeDistId
      return new Promise((reslove, reject) => {
        timeDistId = setInterval(() => {
          if (this._distState) {
            clearInterval(timeDistId)
            timeDistId = null
            reslove("loaded")
          }
        }, 300)
      }).catch((e) => {
        console.log(e)
      })
    },
    /**
     * @description: 缓存常见选项
     * @return: true：有缓存；false: 没有缓存
     */
    cacheDist() {
      let cacheDistOptions = JSON.parse(localStorage.getItem("cacheDist"))
      return cacheDistOptions ? true : false
    },
    // 获取选项数据
    async getDist() {
      // 问号文本
      await this.$store.dispatch("product/getInfo")
      // 选择框选项数据
      await this.$store.dispatch("product/getOptions")
      this._distState = true
    },
    // 根据处方分类以及六级分类设置慢病品种
    filterChronicDiseasesVariety(prescriptionCategory, sixCategory) {
      let sixCategoryName = ""
      for (let item of this.modelAttr.sixCategoryOptions) {
        if (sixCategory == item.id) {
          sixCategoryName = item.dictName.split(" > ").splice(0, 4).join(",")
          this.popoverStr = item.dictName
        }
      }
      if (validateChronicDiseasesVariety(prescriptionCategory, sixCategoryName, this.cfyId)) {
        this.model.chronicDiseasesVariety = 1
      } else {
        this.model.chronicDiseasesVariety = 0
      }
    },
    filterApprovalNo() {
      // this.modelAttr.approvalNoDisabled = false;
      let isFlag = false //判断所属经营范围是否包含保健品
      let isFlag1 = false //判断所属经营范围是否包含化妆品
      let isFlag2 = false //判断所属经营范围是否包含中药饮片
      this.$emit("isShowBtn", this.model.businessScopeList, this.model.spuCategory, this.bjpID)
      for (let item of this.model.businessScopeList) {
        for (let list of item) {
          if (list == this.bjpID) {
            isFlag = true
          }
          if (list == this.hzpID) {
            isFlag1 = true
          }
          if (list == this.zyypID) {
            isFlag2 = true
          }
        }
      }
      this.rules.approvalNo = [
        {
          required: false,
          message: "请输入批准文号",
          trigger: "blur",
        },
      ]

      if (this.model.spuCategory == 3) {
        this.rules.approvalNo = [
          {
            validator: this.validateApprovalNo2,
            required: true,
            trigger: "blur",
          },
        ]

        // 单独校验注册证号和经营范围的匹配
        this.$refs.spuForm.validateField("approvalNo")

        this.approvalNoLabel = "注册证号/备案凭证号"
      } else if (this.model.spuCategory == 1) {
        this.rules.approvalNo = [
          {
            validator: this.validateApprovalNo3,
            required: true,
            trigger: "blur",
          },
        ]
        this.approvalNoLabel = "批准文号"
      } else if (this.model.spuCategory == 2) {
        if (isFlag2) {
          this.model.dosageForm = findIdByOptions("无", "dictName", this.$store.getters.selectOptions.dosageFormOptions)
          this.modelAttr.dosageFormDisabled = true
        } else {
          this.modelAttr.dosageFormDisabled = false
        }
      } else if (this.model.spuCategory == 4) {
        if (isFlag) {
          this.rules.approvalNo = [
            {
              validator: this.validateApprovalNo1,
              required: true,
              trigger: "blur",
            },
          ]
          // if (this.modelAttr.approvalNoDisabled) {
          //   this.modelAttr.approvalNoDisabled = false;
          // }
        } else {
          if (isFlag1) {
            // this.modelAttr.approvalNoDisabled = false;
            this.rules.approvalNo = [
              {
                validator: this.validateApprovalNo10,
                required: true,
                trigger: "blur",
              },
            ]
          } else {
            this.model.approvalNo = this.model.approvalNo || "-";
            // this.modelAttr.approvalNoDisabled = true;
          }
        }
        this.approvalNoLabel = "批准文号/备案凭证号"
      } else {
        this.approvalNoLabel = "批准文号"
      }
      if (
        this.operationType == "detail" ||
        this.operationType == "auditLevel3" ||
        this.operationType == "detailEditImg" ||
        this.operationType == "reuse" ||
        (this.operationType == "draft" && this.$route.query.spuCode) ||
        (this.operationType == "present" && this.model.spuCode)
      ) {
        // this.modelAttr.approvalNoDisabled = true;
      }
    },
    /**
     * @description: 回填SPU数据
     * @return
     */
    async fillDataModel() {
      this.spuLoading = true
      // 如果存在需要格式化的数据
      if (await this.formatSpu()) {
        this.model.spuCategory = this._formatSpu.spuCategory
        // 加载所属经营范围的选项数据
        await this.getbusinessScopeListOptions(this.model.spuCategory)
        // 生产厂家选项数据
        await this.searchManufacturer(this._formatSpu.manufacturerName)
        // debugger
        // 厂家分类回显
        this.manufacturerChange(this._formatSpu.manufacturerName)
        // // 六级分类选项数据
        // await this.getDrugLevelOptions("firstCategory",this._formatSpu.spuCategory)
        // await this.getDrugLevelOptions("secondCategory",this._formatSpu.firstCategory)
        // if(this._updateSecondCategory){ // 同步待跟进二级分类处理，详见formatSpuForUpdate函数
        //   this._formatSpu.secondCategory=findIdByOptions(this._updateSecondCategory,"name",this.modelAttr.secondCategoryOptions);
        // }
        // await this.getDrugLevelOptions("thirdCategory",this._formatSpu.secondCategory)
        // if(this._updateThirdCategory){// 同步待跟进三级分类处理，详见formatSpuForUpdate函数
        //   this._formatSpu.thirdCategory=findIdByOptions(this._updateThirdCategory,"name",this.modelAttr.thirdCategoryOptions);
        // }
        // await this.getDrugLevelOptions("fourthCategory",this._formatSpu.thirdCategory)
        // await this.getDrugLevelOptions("fiveCategory",this._formatSpu.fourthCategory)
        await this.searchCategory("", this._formatSpu.sixCategory)
        for (let key in this.model) {
          if (this._formatSpu.hasOwnProperty(key)) {
            if (
              key == "firstCategory" ||
              key == "secondCategory" ||
              key == "thirdCategory" ||
              key == "fourthCategory" ||
              key == "fiveCategory" ||
              key == "sixCategory"
            ) {
              this.model[key] = this._formatSpu[key] === 0 ? "" : this._formatSpu[key]
              continue
            }
            this.model[key] = this._formatSpu[key]
            if (this.modelAttr.sixCategoryOptions.length == 0) {
              this.model.sixCategory = ""
            }
          }
        }
        // 执行编辑权限初始化之前先备份一份当前可编辑权限副本，方便还原
        this._modelAttr = _.cloneDeep(this.modelAttr)
        //执行可编辑项处理
        this.checkEditPermission()
        await this.$nextTick()
        this.spuLoading = false
      } else {
        await this.$nextTick()
        this.spuLoading = false
        // debugger
        this.$bus.$emit("spuLoading", true) // SPU初始化渲染完成
      }
      // 备份一份初始化后的SPU表单数据，方便数据变更时对比
      this.initializedSpuModel = _.cloneDeep(this.model)
    },
    /**
     * @description: 对比SPU字段跟初始值的变化
     * @param {object} newValue 最新的SPU字段值
     */
    diffSpuModel(newValue) {
      let _newVal = _.cloneDeep(newValue)
      let res = _.isEqual(_newVal, this.initializedSpuModel)
      if (!this.spuLoading && this.initializedSpuModel) {
        this.$bus.$emit("productChange", !res)
      }
      // 对比找出那个属性不一致
      this.findDifferentKeyOfModel(newValue)
    },
    /**
     * @description: 格式化props传入的spuData字段
     * @return: true: 有需要格式化的数据； false :没有需要格式化的数据
     */
    async formatSpu() {
      this._formatSpu = _.cloneDeep(this.spuData)
      if(this._formatSpu.spuCategory === this.TYPEENUM.TRADITIONAL_MEDICINE || this._formatSpu.spuCategory === this.TYPEENUM.GENERAL_MEDICINE) {
        this.selectTargetFirstClass(this._formatSpu.firstCategoryName + " >")
      }
      if (this._formatSpu && Object.keys(this._formatSpu).length > 0) {
        // 同步待跟进时数据处理
        await this.formatSpuForUpdate()
        // 新品上报数据处理
        await this.formatSpuForPresent()
        // 草稿数据回显格式化
        this.formatSpuForDraft()
        // 赠品时的数据处理
        await this.formatSpuForGIFT()
        // 格式化所属经营范围
        if (this._formatSpu.businessScopeListFormat) {
          this._formatSpu.businessScopeList = _.cloneDeep(JSON.parse(this._formatSpu.businessScopeListFormat))
        } else {
          this._formatSpu.businessScopeList = []
        }
        // console.log(this._formatSpu.businessScopeList)
        // 批件图片路径处理
        this._formatSpu.approvalImgList = this._formatSpu.approvalImgList.map((item) => {
          item.url = item.mediaUrl
          item.name = item.mediaName
          return item
        })
        // 特殊属性处理
        if (!this._formatSpu.specialAttrList) {
          this._formatSpu.specialAttrList = []
        }
        this._formatSpu.shadingAttr === -1 ? (this._formatSpu.shadingAttr = "") : ""
        // this._formatSpu.specialAttrList=this._formatSpu.specialAttrList.map(item=>{
        //   return item.specialAttrId;
        // })
        return true
      } else {
        return false
      }
    },
    /**
     * @description: 格式化同步待跟进时数据格式
     * @return:
     */
    async formatSpuForUpdate() {
      // console.log(this._formatSpu)
      if (this.operationType != "update") {
        return false
      }
      // console.log("同步待提交数据格式化")
      //  转换商品分类
      this._formatSpu.spuCategory = findIdByOptions(this._formatSpu.classify, "dictName", this.$store.getters.selectOptions.spuCategoryOptions)
      // 通用名
      this._formatSpu.generalName = this._formatSpu.goodsCommonName
      // 生产厂家
      if (this._formatSpu.manufacturer) {
        await this.searchManufacturer(this._formatSpu.manufacturer)
        if (this.modelAttr.manufacturerOptions.length > 0) {
          this._formatSpu.manufacturerName = this._formatSpu.manufacturer
          this._formatSpu.manufacturer = findIdByOptions(this._formatSpu.classify, "dictName", this.modelAttr.manufacturerOptions)
        } else {
          this._formatSpu.manufacturer = ""
          this._formatSpu.manufacturerName = ""
        }
      }
      // this._formatSpu.manufacturer
      // 批准文号
      this._formatSpu.approvalNo = this._formatSpu.licensePermissionNumber
      // 存储条件
      // switch (this._formatSpu.storageConditionStr){
      //   case "无":
      //   this._formatSpu.storageCond=0;
      //     break;
      //   case "避光":
      //   this._formatSpu.storageCond=1;
      //     break;
      //   case "遮光":
      //   this._formatSpu.storageCond=2;
      //     break;
      //   case "凉暗":
      //   this._formatSpu.storageCond=3;
      //     break;
      //   default:
      //     this._formatSpu.storageCond="";
      //     break;
      // }

      // 进项税率
      this._formatSpu.inRate = findIdByOptions(this._formatSpu.incomeTaxRate, "dictName", this.$store.getters.selectOptions.inRateOptions)
      // 销项税率
      this._formatSpu.outRate = findIdByOptions(this._formatSpu.ouputTaxRate, "dictName", this.$store.getters.selectOptions.inRateOptions)
      // 剂型
      this._formatSpu.dosageForm = findIdByOptions(
        this._formatSpu.dosageFormName,
        "dictName",
        this.$store.getters.selectOptions.dosageFormOptions
      )
      //经营范围
      this._formatSpu.businessScopeList = []
      // 经营范围格式化数据是字符串
      this._formatSpu.businessScopeListFormat = "[]"
      // 批件图片
      this._formatSpu.approvalImgList = []
      // 无小包装条码设置默认值
      this._formatSpu.noSmallPackageCode = 0
    },
	valueInput(formKey, key, value, regExp) {
		console.log(this.goodsBasicComVoRules);
		if (new RegExp(regExp).test(value)) {
			this[formKey][key] = value
		}
	},
    manufacturerFocus(e) {
      if (e) {
        this.$refs.manufacturerSelect.selectedLabel = this.model.manufacturerName
        this.searchManufacturer(this.model.manufacturerName)
      }
    },
    /**
     * @description: 新品上报数据格式化
     */
    async formatSpuForPresent() {
      if (this.operationType != "present") {
        return false
      }
      // 剂型
      let isdosageForm = findIdByOptions(this._formatSpu.dosageFormName, "dictName", this.$store.getters.selectOptions.dosageFormOptions)
      if (isdosageForm) {
        this._formatSpu.dosageForm = isdosageForm
      }
      // 生产厂家
      if (this._formatSpu.manufacturer && this._formatSpu.manufacturerName) {
        await this.searchManufacturer(this._formatSpu.manufacturerName)
      }
      if (!this._formatSpu.spuCode) {
        //经营范围
        this._formatSpu.businessScopeList = []
        // 经营范围格式化数据是字符串
        this._formatSpu.businessScopeListFormat = "[]"
        // 无小包装条码设置默认值
      }
      this._formatSpu.noSmallPackageCode = 0
    },
    // 草稿数据回显格式化
    formatSpuForDraft() {
      if (this.operationType != "draft") {
        return
      }
      // 经营范围格式化数据是字符串
      !this._formatSpu.businessScopeListFormat ? (this._formatSpu.businessScopeListFormat = "[]") : ""
      this._formatSpu.shadingAttr === -1 ? (this._formatSpu.shadingAttr = "") : ""
      this._formatSpu.spuCategory === 0 ? (this._formatSpu.spuCategory = "") : ""
      this._formatSpu.dosageForm === 0 ? (this._formatSpu.dosageForm = "") : ""
      // 六级分类
      this._formatSpu.firstCategory === 0 ? (this._formatSpu.firstCategory = "") : ""
      this._formatSpu.secondCategory === 0 ? (this._formatSpu.secondCategory = "") : ""
      this._formatSpu.thirdCategory === 0 ? (this._formatSpu.thirdCategory = "") : ""
      this._formatSpu.fourthCategory === 0 ? (this._formatSpu.fourthCategory = "") : ""
      this._formatSpu.fiveCategory === 0 ? (this._formatSpu.fiveCategory = "") : ""
      this._formatSpu.sixCategory === 0 ? (this._formatSpu.sixCategory = "") : ""
      // this._formatSpu.largeCategory === 0 ? this._formatSpu.largeCategory="" :"";
      // this._formatSpu.dosageForm === 0 ? this._formatSpu.dosageForm="" :"";
      this._formatSpu.manufacturer === 0 ? (this._formatSpu.manufacturer = "") : ""
      // this._formatSpu.storageCond === 0 ? this._formatSpu.storageCond="" :"";
    },
    /**
     * @description: 格式化赠品数据格式
     */
    async formatSpuForGIFT() {
      // TYPEENUM: {
      //   EMPTY: "", //未选择
      //   GIFT: 6, //赠品
      //   NOT_MEDICINE: 5, //非药
      //   MEDICAL_INSTRUMENT: 4, //医疗器械
      //   TRADITIONAL_MEDICINE: 3, //中药
      //   GENERAL_MEDICINE: 1 //普通药品
      // },
      this.spuLoading = true
      if (this._formatSpu.spuCategory == this.TYPEENUM.GIFT && this.skuData.length > 0) {
        this._formatSpu.brand = this.skuData[0].brand //品牌商标
        this._formatSpu.packageUnit = this.skuData[0].packageUnit === 0 ? "" : this.skuData[0].packageUnit // 包装单位
        this._formatSpu.spec = this.skuData[0].spec // 规格型号
        this._formatSpu.suggestedPrice = this.skuData[0].suggestedPrice // 建议零售
        // this._formatSpu.storageCond === 0 ? this._formatSpu.storageCond="" :""; //存储条件

        this._formatSpu.delegationProduct = this.skuData[0].delegationProduct // 是否委托生产
        await this.searchEntrustedManufacturer(this.skuData[0].entrustedManufacturerName)
        this.skuData[0].entrustedManufacturer
          ? (this._formatSpu.entrustedManufacturer = this.skuData[0].entrustedManufacturer)
          : (this._formatSpu.entrustedManufacturer = "")

        let smallPackageCodeList = this.skuData[0].smallPackageCodeList // 小包装条码
        let mediumPackageCodeList = this.skuData[0].mediumPackageCodeList // 中包装条码
        let piecePackageCodeList = this.skuData[0].piecePackageCodeList // 件包装条码
        this._formatSpu.noSmallPackageCode = this.skuData[0].noSmallPackageCode
        this._formatSpu.smallPackageCodeList = smallPackageCodeList ? smallPackageCodeList : []
        this._formatSpu.mediumPackageCodeList = mediumPackageCodeList ? mediumPackageCodeList : []
        this._formatSpu.piecePackageCodeList = piecePackageCodeList ? piecePackageCodeList : []

        // 有效期相关处理
        this._formatSpu.validityTransition = this.skuData[0].validity
        this._formatSpu.validity = validityTransition(this.skuData[0].validity)
      } else {
        return false
      }
    },

    /**
     * @description: 根据 editPermission 执行可编辑项处理
     */
    checkEditPermission() {
      // 执行编辑权限初始化之前先备份一份当前可编辑权限副本，方便还原
      this._modelAttr = _.cloneDeep(this.modelAttr)
      // 复用 || 详情 || 复用后保存的草稿
      if (
        this.operationType == "reuse" ||
        this.operationType == "detail" ||
        (this.operationType == "present" && this.model.spuCode) ||
        (this.operationType == "draft" && this.$route.query.spuCode) ||
        this.operationType == "auditLevel3" ||
        this.operationType == "detailEditImg"
      ) {
        let modelArr = Object.keys(this._model)
        for (let key of modelArr) {
          // 禁用所有数据
          this.modelAttr[key + "Disabled"] = true
        }
        if (this.operationType == "reuse" || (this.operationType == "draft" && this.$route.query.spuCode)) {
          // 复用时批件图片不禁用
          this.modelAttr["approvalImgListDisabled"] = false
        }
        if (this.operationType == "auditLevel3") {
          this.modelAttr["taxCategoryCodeDisabled"] = false
          this.modelAttr["inRateDisabled"] = false
          this.modelAttr["outRateDisabled"] = false
        }
      }

      // 草稿
      if (this.operationType == "draft") {
        this.checkReuseSpu() ? this.disabledAllSpu() : ""
      }

      // 驳回修改
      if (this.operationType == "RejectEdit") {
        this.checkReuseSpu() ? this.disabledAllSpu() : ""
        this.modelAttr.spuCodeBtnDisplay = false
        // this.editPermission.push("spuCategory")
      }

      // 编辑
      if (this.operationType == "edit") {
        this.modelAttr.spuCodeBtnDisplay = false
      }

      // 详情
      if (this.operationType == "detail") {
        this.modelAttr.spuCodeBtnDisplay = false
      }

      // 预首营修改
      if (this.operationType == "operate") {
        for (let key in this.rules) {
          let rule = this.rules[key]
          if (rule[0].required) {
            this.editPermission.push(key)
          }
        }
      }

      // 二审
      if (this.operationType == "auditLevel2") {
        // 如果为子公司 && 存在复用SPU
        if (this.auditData && this.auditData.applyScope == 4 && this.checkReuseSpu()) {
          this.modelAttr.spuCodeBtnDisplay = false
        } else {
          this.checkReuseSpu() ? this.disabledAllSpu() : ""
          this.modelAttr["approvalImgListDisabled"] = false
        }
      }

      // 一审
      if (this.operationType == "auditLevel1") {
        // 如果为子公司 && 存在复用SPU
        if (this.auditData && this.auditData.applyScope == 4 && this.checkReuseSpu()) {
          this.modelAttr.spuCodeBtnDisplay = false
        } else {
          this.checkReuseSpu() ? this.disabledAllSpu() : ""
          this.modelAttr.spuCodeBtnDisplay = false
          this.editPermission = [
            // "spuCategory",//商品分类
            // "largeCategory",//商品大类
            "spuCode", //商品编码
            "generalName", //通用名
            "generalNameCode", //通用名助记码
            "instructionSpec", //批件规格
            "spec", //规格型号（赠品）
            "approvalNo", //批准文号
            "manufacturer", //商品分类
            "delegationProduct", //是否委托生产
            "entrustedManufacturer", //委托生产厂家
            "validity", //有效期
            "dosageForm", //剂型
            "businessScopeList", //商品分类
            "suggestedPrice", //建议零售价
            "specialAttrList", //特殊属性
            "inRate", //进项税率
            "outRate", //销项税率
            "packageUnit", //包装单位
            "marketAuthor", //上市许可持有人
            "marketAuthorAddress",
            "brand", //品牌商标
            "noSmallPackageCode", //无小包装条码
            "smallPackageCodeList", //小包装条码
            "mediumPackageCodeList", //中包装条码
            "piecePackageCodeList", //件包装条码
            "shadingAttr", //存储属性
            // "storage",//贮藏
            "whetherSupervision", //是否监管
            "chronicDiseasesVariety", //慢病品种
            // "approvalImgList",//批件图片
            "taxCategoryCode", //税务分类编码
            "manufacturingLicenseNo",//生产许可编号
          ]
        }
      }

      // 剂型值在 非药，医疗器械是 其他
      if (this.model.spuCategory == this.TYPEENUM.NOT_MEDICINE || this.model.spuCategory == this.TYPEENUM.MEDICAL_INSTRUMENT) {
		this.model.dosageForm = findIdByOptions("其他", "dictName", this.$store.getters.selectOptions.dosageFormOptions)
        this.editPermission.push("dosageForm")
      }
      if (this.editPermission.length > 0) {
        for (let item of this.editPermission) {
          this.modelAttr[item + "Disabled"] = true
        }
      }
      this.$nextTick(() => {
        this.spuLoading = false //确保所有数据渲染完成再关闭加载层
        this.$refs["spuForm"].clearValidate() //清除校验提示
        this.$bus.$emit("spuLoading", true) // SPU初始化渲染完成
      })
    },
    /**
     * @description: 检测是否为复用的SPU
     */
    /**
     * @description: 检测是否为复用的SPU
     * @param {type}
     * @return: true : 存在 false: 不存在
     */
    checkReuseSpu() {
      for (let skuItem of this.skuData) {
        if (skuItem.spuMultiplex == 1) {
          return true
        }
      }
      for (let sauItem of this.sauData) {
        if (sauItem.skuMultiplex == 1) {
          return true
        }
      }
      return false
    },
    disabledAllSpu() {
      let modelArr = Object.keys(this._model)
      for (let key of modelArr) {
        // 禁用所有数据
        this.modelAttr[key + "Disabled"] = true
      }
    },
    /**
     * @description: 查询所属经营范围选项数据
     * @param {number} spuCategory 商品大类ID
     * @return:
     */
    filterBusinessScopeListOptions(arr) {
      arr.forEach((item) => {
        if (item.children) {
          let index = item.children.findIndex((item) => item.isValid)
          item.disable = Number(index) == -1 ? false : true
          this.filterBusinessScopeListOptions(item.children)
        } else {
          item.disable = item.isValid ? false : true
        }
      })
    },
    async getbusinessScopeListOptions(spuCategory) {
      try {
        // 所属经营范围
        let res = await getTotalDictionaryTree({
          type: "11",
          parentId: spuCategory,
        })
        this.modelAttr.businessScopeListOptions = res.data
        this.filterBusinessScopeListOptions(this.modelAttr.businessScopeListOptions)
        this.modelAttr.businessScopeListOptions.forEach((item) => {
          if (item.dictName == "保健食品") {
            this.bjpID = item.id
          }
          if (item.dictName == "化妆品") {
            this.hzpID = item.id
          }
          if (item.dictName == "中药饮片") {
            this.zyypID = item.id
          }
          if (item.children) {
            item.children.forEach((list) => {
              if (list.dictName == "保健食品") {
                this.bjpID = list.id
              }
              if (list.dictName == "化妆品") {
                this.hzpID = list.id
              }
              if (list.dictName == "中药饮片") {
                this.zyypID = list.id
              }
            })
          }
        })
        getId(res.data, businessScopeListOptionsParentId)
        if(this.$route.query.pageType != 'detail'){
          if(spuCategory == 2){
            // console.log('====================================');
            // console.log(res.data, "this.modelAttr.businessScopeListOptions");
            // console.log('====================================');
            this.CNRangeName = this.modelAttr.businessScopeListOptions.find(item => item.dictName === '中药')
            this.CNNoLimitedRangeName = this.CNRangeName.children.find(item => item.dictName === '中药饮片')
            this.CNLimitedRangeName = this.CNRangeName.children.find(item => item.dictName === "中药饮片(限品种)")
            this.autoFillInfo(spuCategory, '', 'NO_CHANGE_CATEGORY')
          }else if(spuCategory == 4){
            this.foodRange = this.modelAttr.businessScopeListOptions.find(item => item.dictName === '食品')
            this.otherRange = this.modelAttr.businessScopeListOptions.find(item => item.dictName === '其它')
            this.disinfectantProductsRange = this.modelAttr.businessScopeListOptions.find(item => item.dictName === '消毒产品')
            this.cosmeticsRange = this.modelAttr.businessScopeListOptions.find(item => item.dictName === '化妆品')
          }
        }
        this.resetKey++
      } catch (error) {
        console.log(error)
      }
    },
    /**
     * @description: 动态加载六级分类选项
     * @param {string} level "firstCategory","secondCategory","thirdCategory","fourthCategory","fiveCategory","sixCategory"
     * @param {string} parentId 上级分类ID
     * @param {boolen} isSpuDrawerForm  true : spu复用查询时的数据
     * @return: promise
     */
    async getDrugLevelOptions(level, parentId, isSpuDrawerForm) {
      // 如果不存在parentId，则不执行对应的逻辑
      if (!parentId) {
        return false
      }
      let levelMap = {
        firstCategory: 1,
        secondCategory: 2,
        thirdCategory: 3,
        fourthCategory: 4,
        fiveCategory: 5,
        sixCategory: 6,
      }
      let attr = "modelAttr"
      if (isSpuDrawerForm) {
        // spu复用查询时的数据
        attr = "spuDrawerFormOptions"
      }
      switch (level) {
        case "firstCategory": // 一级分类特殊判断，不会在SPU复用查询表单时触发，复用查询表单一级分类选项是全部值
          this[attr].firstCategoryLoad = true
          var options = await categoryList({
            isValid: "1",
            level: 1,
            // 注意：一级分类时，parentId对应的是当前选中的商品大类ID
            spuCategory: parentId,
          })
          this[attr].firstCategoryOptions = options.data
          this[attr].firstCategoryDisabled = false
          this[attr].firstCategoryLoad = false
          // 重置所有分类选项
          for (let key in levelMap) {
            if (key !== "firstCategory") {
              this[attr][key + "Options"] = []
              this[attr][key + "Disabled"] = true
              this.model[key] = ""
            }
          }
          break
        default:
          if (parentId) {
            this[attr][level + "Load"] = true
            var options = await categoryList({
              isValid: "1",
              level: levelMap[level],
              parentId: parentId,
            })
            this[attr][level + "Options"] = options.data
            this[attr][level + "Disabled"] = false
            this[attr][level + "Load"] = false
          }
          break
      }
    },
    /**
     * @description: 查询六级分类字典
     * @param {string} inputValue 查询文本
     * @param {string} id id
     * @return: undefined
     */
    async searchCategory(inputValue, id) {
      this.modelAttr.sixCategoryLoad = true
      let res = await searchSixCategory({
        dictName: inputValue,
        // id:1,
        id: id ? id : "",
        spuCategory: this.model.spuCategory,
      })
      if (res.retMsg) {
        this.modelAttr.sixCategoryOptions = []
      } else {
        this.modelAttr.sixCategoryOptions = res
      }
      console.log(res)
      this.modelAttr.sixCategoryLoad = false
    },
    /**
     * @description: 根据商品分类判断是否显示对应的SPU字段
     * @param {array} spuCategoryAry 商品分类数组，数组包含的商品分类显示下方参数 modelField 对应的SPU字段
     * @param {array} modelField  SPU字段名称
     * @return:{function} 根据 spuCategoryAry 与 modelField 计算是否展示的将箭头函数
     */
    checkStateByspuCategory(spuCategoryAry, modelField) {
      return (spuCategoryAry, modelField) => {
        let res = spuCategoryAry.includes(this.model.spuCategory)
        modelField.split(",").forEach((item) => {
          this.modelAttr[item + "Display"] = res
        })
        if (!res) {
          this.resetModeValue(modelField)
        }
        return res
      }
    },
    /**
     * @description: 重置参数中的表单选项值为初始值
     * @param {array} spukey SPU字段中需要重置的KEY
     * @return:
     */
    resetModeValue(spukey) {
      this.model[spukey] = _defaultDataModel[spukey]
    },
    /**
     * @description: 模糊查询委托生产厂家
     * @param {string} inputValue 查询文本
     * @return: undefined
     */
    async searchEntrustedManufacturer(inputValue) {
      // console.log("1111")
      let distManufacturer = await dictSearchTypeAndName({
        dictName: inputValue ? inputValue : "药",
        type: 12,
      })
      this.modelAttr.entrustedManufacturerOptions = distManufacturer.list
    },
    /**
     * @description: 模糊查询生产厂家
     * @param {string} inputValue 查询文本
     * @return: undefined
     */
    async searchManufacturer(inputValue) {
      // console.log("1111")
      let distManufacturer = await dictSearchTypeAndName({
        dictName: inputValue ? inputValue : "药",
        type: 12,
      })
      this.modelAttr.manufacturerOptions = distManufacturer.list
    },
    /**
     * @description: 模糊查询生产厂家 （spu复用form）
     * @param {string} inputValue 查询文本
     * @return: undefined
     */
    async searchManufacturerForDrawerForm(inputValue) {
      let distManufacturer = await dictSearchTypeAndName({
        dictName: inputValue ? inputValue : "药",
        type: 12,
      })
      this.spuDrawerFormOptions.manufacturerOptions = distManufacturer.list
    },
    // 生产厂家当前选项值
    manufacturerChange(data, type) {
      // 设置厂家分类选项
      for (let item of this.modelAttr.manufacturerOptions) {
        if (item.dictName == data) {
          this.model.manufacturerCategoryName = item.manufacturerCategoryName
          this._formatSpu.manufacturer = item.id
          this.model.manufacturer = item.id
          if(type != 'handel'){
            this.model.productionAddress = item.productionAddress
          }
        }
      }
    },
    /**
     * spu编码复用
     */
    spuCodeMultiplexing() {
      // spu复用初始化生产厂家数据
      if (this.model.approvalNo != "-" && this.model.approvalNo != "" && this.model.approvalNo != null && this.model.approvalNo != 0) {
        this.$refs.spuDrawerTable.spuDrawerForm.approvalNo = this.model.approvalNo
      } else {
        this.$refs.spuDrawerTable.spuDrawerForm.approvalNo = ""
        this.$refs.spuDrawerTable.spuDrawerForm.generalName = this.model.generalName
        this.$refs.spuDrawerTable.spuDrawerForm.generalNameCode = this.model.generalNameCode
        this.$refs.spuDrawerTable.spuDrawerForm.manufacturer = this.model.manufacturerName

      }
      this.$refs.spuDrawerTable.show()
    },
    saveSpu(spuInfo) {
      if (this.operationType == "present" && !!this.model.approvalNo && spuInfo.approvalNo.indexOf(this.model.approvalNo) == -1) {
        this.$message.error("新品上报批准文号不同不可复用")
        return
      }
      this.stopAutoFillInfo = true
      this.$emit("reuseSpu", spuInfo.spuCode)
      this.$bus.$emit("reuseEvent", spuInfo.spuCode)
      this.$refs.spuDrawerTable.cancelSpuCode()
      if (this.model.spuCategory === 1 || this.model.spuCategory === 2) {
        this.$bus.$emit("generalNameChange", spuInfo.spuCode)
      }
      this.spuLoading = true
    },
    /**
     * SPU表单重置
     * val 商品分类值
     */
    resetForm(val) {
      // 重置选项
      // for (let key in this.model){
      //   if(key!="spuCategory"){ // 商品分类不重置
      //     this.model[key]=_.cloneDeep(this._model[key])
      //   }
      // }
      // 清空六级分类的值
      this.model.sixCategory = ""
      this.modelAttr.sixCategoryOptions = []
      // TYPEENUM: {
      //   EMPTY: "", //未选择
      //   GIFT: 6, //赠品
      //   NOT_MEDICINE: 5, //非药
      //   MEDICAL_INSTRUMENT: 4, //医疗器械
      //   TRADITIONAL_MEDICINE: 3, //中药
      //   GENERAL_MEDICINE: 1 //普通药品
      // },
      // 剂型值在 非药，医疗器械是 其他
      if (val == this.TYPEENUM.NOT_MEDICINE || val == this.TYPEENUM.MEDICAL_INSTRUMENT) {
        this.model.dosageForm = findIdByOptions("其他", "dictName", this.$store.getters.selectOptions.dosageFormOptions)
        this.modelAttr.dosageFormDisabled = true
      } else {
        this.model.dosageForm = ""
        this.modelAttr.dosageFormDisabled = false
      }
      //上市许可持有人跟普通药品管理处理
      if (
        val == this.TYPEENUM.GENERAL_MEDICINE &&
        (this.operationType == "add" || this.operationType == "update" || this.operationType == "present")
      ) {
        // 给上市许可持有人赋值最近一次的生产厂家值
        this.model.marketAuthor = this._marketAuthor ? this._marketAuthor : ""
        this.model.marketAuthorAddress = this._marketAuthorAddress? this._marketAuthorAddress : ""
        // this.modelAttr.marketAuthorDisabled=true;
      } else {
        // 重置为初始值
        this.model.marketAuthor = this._model.marketAuthor
        this.model.marketAuthorAddress = this._model.marketAuthorAddress
        // this.modelAttr.marketAuthorDisabled=false;
      }
      // 重置所属经营范围值
      this.businessScopeList = []
      // 重置税率和税务分类编码
      this.resetSixCategoryChange()
      this.$nextTick(() => {
        this.$refs["spuForm"].clearValidate() //清除校验提示
      })
    },
    /**
     * 父组件获取 SPU数据
     * @return {object} state：是否通过校验；data：SPU数据
     * @param {boolen} isDraft  true:保存草稿，false:提交
     */
    getSpuData(isDraft) {
      return new Promise((resolve, reject) => {
        if (isDraft) {
          // 保存草稿时
          resolve({
            state: true,
            data: this.formatSpuForSubmit(), //获取格式化数据
          })
        } else {
          this.$refs["spuForm"].validate((valid) => {
            if (valid && this.checkRateAndTaxCategoryCode()) {
              //校验通过
              resolve({
                state: valid,
                data: this.formatSpuForSubmit(), //获取格式化数据
              })
            } else {
              resolve({
                state: false,
                data: this.model,
              })
            }
          })
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    /**
     * 格式化提交SPU数据
     * @return {object} 提交的SPU数据
     */
    formatSpuForSubmit() {
      let submitSpu = {}

      // 格式化提交的SPU数据
      for (let key in this.model) {
        if (this.modelAttr[key + "Display"]) {
          // 所属经营范围调整
          if (key == "businessScopeList") {
            // 取出选项中的数组
            let arr = []
            // let flattenArr = [];
            for (let item of this.model[key]) {
              if (Array.isArray(item)) {
                arr.push(item[item.length - 1])
              }
            }
            // 展开数组
            // flattenArr = _.flattenDeep(this.model[key]);
            // 移除父ID pull: 移除数组array中所有和给定值相等的元素，使用 SameValueZero 进行全等比较。 参数1 要修改的数组， 参数2 要删除的值
            // _.pull(flattenArr, ...businessScopeListOptionsParentId);
            submitSpu.businessScopeList = arr
            continue
          }
          // 赠品有效期
          if (key == "validity") {
            submitSpu[key] = this.model[key + "Transition"]
            continue
          }
          submitSpu[key] = this.model[key]
        }
        if(key === 'manufacturingLicenseNo'){
          submitSpu[key] = this.model[key]
        }
        if(key === 'manufacturerName'){
          submitSpu[key] = this.model[key]
        }
      }
      // 记录商品修改时改动的字段KEY
      submitSpu.productFieldRecord = {
        spuFieldList: this.changeList,
        skuFieldList: [],
        extendFieldList: [],
      }
      return submitSpu
    },
    /**
     * @description: 商品分类切换后，调整字段校验逻辑
     * @param {array} names 需要调整校验逻辑的字段 key
     * @param {boolen} verify true: names数组中的项设置为校验， false:设置为不校验
     * @return:
     */
    rulesChange(names, verify) {
      // 为每一项字段执行校验规则变更处理
      for (let i = 0; i < names.length; i++) {
        this.rules[names[i]].forEach((item) => {
          if (item.hasOwnProperty("required")) {
            verify ? (item.required = true) : (item.required = false)
          }
        })
      }
    },
    /**
     * @description:select开启可搜索以及多选后按钮问题临时修复 （只有开启搜索和多选的需要）
     * 详见：https://github.com/ElemeFE/element/issues/13140#issuecomment-433279443
     * @param {string} select 需要此功能的 select ref
     * @param: {string} eventType 事件触发类型 blur，focus, click
     * @param: {boolen} click 是否为模拟click 触发
     */
    fixSelectArrowByFilterableAndMultiple(select, eventType, click) {
      // console.log(eventType,click)
      // debugger
      if (eventType != "click") {
        if (eventType == "focus") {
          this.$refs[select].canClose = true
        } else {
          // this.$refs[select].canClose=true;
        }
        return false
      }
      if (this.$refs[select].canClose) {
        this.$refs[select].canClose = false
        this.$refs[select].blur()
      } else {
        this.$refs[select].canClose = true
      }
    },
    // // 清除本位码校验
    // clearValidateStandardCodeList(){
    //   this.$refs.standardCodeList.clearValidate();
    // },
    // 批件图片预览
    approvalPreview() {
      if (this.model.approvalImgList.length > 0) {
        this.modelAttr.approvalImgPreview = true
      }
    },
    // 关闭批件图片预览
    closeApprovalViewer() {
      this.modelAttr.approvalImgPreview = false
    },
    /**
     * @description:保存批件图片
     * @param
     * @return:
     */
    saveApproval() {
      // debugger
      let isUpLoad = false
      if (this._approvalImgList) {
        isUpLoad = true
        for (let item of this._approvalImgList) {
          if (item.status != "success") {
            isUpLoad = false
            break
          }
          if (item.hasOwnProperty("response") && item.response.retCode == 0) {
            item.mediaUrl = item.response.data.mediaUrl
            item.mediaName = item.response.data.mediaName
            item.pictureHeight = item.response.data.pictureHeight
            item.pictureWidth = item.response.data.pictureWidth
            item.meidiaType = 0
            delete item.raw
            delete item.name
            delete item.percentage
            delete item.response
            delete item.size
          }
        }
      } else {
        //打开上传弹层什么也不做时
        this.modelAttr.approvalImgUpload = false
        return false
      }
      if (isUpLoad) {
        this.model.approvalImgList = _.cloneDeep(this._approvalImgList)
        this._approvalImgList = null
        //上传组件内容不更新问题
        // this.$refs.approvalImg.list=this._approvalImgList;
        this.modelAttr.approvalImgUpload = false
      } else {
        this.$message.error("图片上传中，请耐心等待")
      }
    },
    beforeApprovalImgUploadOpen() {
      // 拷贝一份数据,当批件图片被修改后不做任何操作，或者直接取消时，方便数据还原回显
      this.cloneApprovalImgList = _.cloneDeep(this.model.approvalImgList)
    },
    // 批件上传取消
    cancelApproval() {
      // 取消操作时还原数据
      this.model.approvalImgList = this.cloneApprovalImgList
      this.cloneApprovalImgList = null
      this.modelAttr.approvalImgUpload = false
    },
    // 处理图片接口结构
    handleImgData(list) {
      let flag = true
      let arr = []
      for (let item of list) {
        // 防止图片未上传完成
        if (item.status == "uploading") {
          this.$message.error("图片上传中，请稍后再试...")
          flag = false
          break
        }
        if (item.status == "success") {
          if (item.response) {
            let { mediaName, mediaUrl, meidiaType } = item.response.data
            arr.push({ mediaName, mediaUrl, meidiaType: 0 })
          } else {
            arr.push({
              mediaName: item.mediaName ? item.mediaName : item.name,
              mediaUrl: item.mediaUrl ? item.mediaUrl : item.url,
              meidiaType: 0, // 媒体类型(0:图片, 1:视频)
            })
          }
          flag = true
        }
      }
      return { flag, arr }
    },
    /**
     * @description: 批件图片上传完成
     * @param {array} imageList
     * @return:
     */
    uploadSuccess(imageList) {
      this._approvalImgList = imageList
      let { flag, arr } = this.handleImgData(imageList)
      if (!flag) return
      this.model.approvalImgList = arr
    },
    /**
     * @description: 查找mode 中新旧值不同的KEY
     * @param {object} newValue 新值
     * @return:
     */
    findDifferentKeyOfModel(newValue) {
      if (
        this.$route.query.procKey === "商品新增流程" ||
        this.$route.query.procKey === "新品上报流程" ||
        this.operationType == "edit" ||
        this.operationType == "operate" ||
        // 审核及审核驳回修改时需要判断是否为修改商品
        (this.operationType == "auditLevel1" && this.$route.query.approvalProcess == 1) ||
        (this.operationType == "auditLevel2" && this.$route.query.approvalProcess == 1) ||
        // 判断是否为商品上架状态修改
        (this.operationType == "auditLevel2" && this.$route.query.type == "shelves") ||
        (this.operationType == "RejectEdit" && this.$route.query.approvalProcess == 1)
      ) {
        for (let key in newValue) {
          // initializedSpuModel 初始化后备份的SPU数据对象
          let res = _.isEqual(newValue[key], this.initializedSpuModel[key])
          if (!res && !this.changeList.includes(key)) {
            this.changeList.push(key)
          } else if (_.isEqual(this.model[key], this.spuData[key])) {
            this.changeList.remove(key)
          }
        }
      }
    },
    /**
     * @description: 通用名相关逻辑处理 （设置可输入最大值为字节，触发 generalNameChange 事件）
     * @param {type}
     * @return:
     */
    generalNameHandle() {
      // this.model.generalName=setMaxLengthByByte(this.model.generalName,78)
      // this.model.generalNameCode = pinTo.getPinYinFirstCharacter(this.model.generalName);
      // if(this.operationType=="add" || this.operationType=="draft" || this.operationType=="reuse"){
      //   this.$bus.$emit("generalNameChange",this.model.generalName)
      // }
      // debugger
      this._generalNameHandle = debounce(() => {
        this.model.generalName = setMaxLengthByByte(this.model.generalName, 200)
        this.model.generalNameCode = pinTo.getPinYinFirstCharacter(this.model.generalName)
        this.initializedSpuModel.generalNameCode = this.model.generalNameCode
        if (this.operationType == "add" || this.operationType == "draft" || this.operationType == "reuse") {
          this.$bus.$emit("generalNameChange", this.model.spuCode)
        }
        this.initializedSpuModel.generalName = this.model.generalName
        this.initializedSpuModel.generalNameCode = this.model.generalNameCode
      }, 300)
    },
    debounce,
    /**
     * @description: 六级分类变更触发
     * @param {number} id : 当前选中的选项ID
     * @return:
     */
    sixCategoryChange(id) {
      let title = ""
      for (let item of this.modelAttr.sixCategoryOptions) {
        if (item.id == id) {
          title = item.dictName
          this.popoverStr = item.dictName
        }
      }
      if(this.model.spuCategory === this.TYPEENUM.TRADITIONAL_MEDICINE || this.model.spuCategory === this.TYPEENUM.GENERAL_MEDICINE) { 
        this.selectTargetFirstClass(title)
      }
      this.filterChronicDiseasesVariety(this.skuForm.prescriptionCategory, id)
      // 六级分类包含避孕套这个关键字的，税率自动变为0%，税收分类编码自动变为*********，两者均不可修改
      if (title.includes("避孕套")) {
        if (this.model.approvalNo.includes("国药准字S")) {
          this.$message.error("避孕套与批准文号冲突，请重新维护")
          this.model.approvalNo = ""
          this.model.sixCategory = ""
          return false
        }
        this.setRate("0%")
        this.model.taxCategoryCode = "*********"
        this.modelAttr.taxCategoryCodeDisabled = true
        this.modelAttr.inRateDisabled = true
        this.modelAttr.outRateDisabled = true
      } else {
        this.resetSixCategoryChange()
      }
      this.editRateFunction()
    },
    /**
     * @description: 重置税率和税务分类编码
     */
    resetSixCategoryChange() {
      this.model.taxCategoryCode = ""
      this.modelAttr.taxCategoryCodeDisabled = false
      this.modelAttr.inRateDisabled = false
      this.modelAttr.outRateDisabled = false
    },
    /**
     * @description: 找出1级分类为：配方饮品 或 养生中药 或 中药材；
     * @param {string} val : 分类名
     * @return:
     */
    selectTargetFirstClass(val) {
      const str = val.split(/\s*>\s*/)[0].trim();
      if(str === "配方饮片" || str === "养生中药" || str === "中药材") {
        this.$store.commit("product/SET_UNIT_TARGET_CATEGORY", true)
      }else {
        this.$store.commit("product/SET_UNIT_TARGET_CATEGORY", false)
      }
      if(str === '注射用药') {
        this.$store.commit("product/SET_UNIT_ZSYY_CATEGORY", true)
      }else {
        this.$store.commit("product/SET_UNIT_ZSYY_CATEGORY", false)
      }
    },
    /**
     * @description: 设置进项税率和销项税率为指定值
     * @param {string} val :需要设置的百分比值 如 0% ，10%
     * @return:
     */
    setRate(val) {
      for (let item of this.$store.getters.selectOptions.inRateOptions) {
        if (item.dictName == val) {
          this.model.inRate = item.id
          this.model.outRate = item.id
        }
      }
    },
    /**
     * @description: 判断剂型选择中药饮片的，税率和税收分类编码需人工维护，系统校验维护的值为以上四种之一，如果不满足条件，提示：
     * 植物类中药饮片，税率9%，税收分类编码*********；
     * 动物类中药饮片，税率9%，税收分类编码*********；
     * 矿物类中药饮片，税率13%，税收分类编码*********；
     * 其他中药饮片，税率13%，税收分类编码*********。
     * @return { boolean } 是否符合校验条件
     */
    checkRateAndTaxCategoryCode() {
      if (this.model.spuCategory == 3 && this.model.filingsAuthor) {
        if (/^[0-9]*$/.test(this.model.filingsAuthor) || /^[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、 ]*$/.test(this.model.filingsAuthor)) {
          this.$message.error('医疗器械注册人/备案人名称格式错误')
          return false
        }
      }
      let dosageFormName = findNameByOptions(this.model.dosageForm, "id", this.$store.getters.selectOptions.dosageFormOptions)
      if (dosageFormName != "中药饮片") {
        return true
      }
      let arr1 = ["*********", "*********", "*********"]
      let arr2 = ["*********"] 
      let inRate = findNameByOptions(this.model.inRate, "id", this.$store.getters.selectOptions.inRateOptions)
      let outRate = findNameByOptions(this.model.outRate, "id", this.$store.getters.selectOptions.inRateOptions)
      if (inRate == outRate && outRate == "9%" && arr2.includes(this.model.taxCategoryCode)) {
        return true
      }
      if (inRate == outRate && outRate == "13%" && arr1.includes(this.model.taxCategoryCode)) {
        return true
      }
      // this.$message.error("植物类中药饮片，税率9%，税收分类编码*********；动物类中药饮片，税率9%，税收分类编码*********；矿物类中药饮片，税率13%，税收分类编码*********；其他中药饮片，税率13%，税收分类编码*********。")
      this.$alert(
        `<p style="text-align: center">植物类中药饮片，税率9%，税收分类编码*********</p>
                     <p style="text-align: center">动物类中药饮片，税率13%，税收分类编码*********</p>
                     <p style="text-align: center">矿物类中药饮片，税率13%，税收分类编码*********</p>
                     <p style="text-align: center">其他中药饮片，税率13%，税收分类编码*********</p>
        `,
        {
          dangerouslyUseHTMLString: true,
          showConfirmButton: false,
          // confirmButtonText: '确定',
        }
      )
      return false
    },
    changeBusiness(e) {
      if (e.length && (e[e.length - 1].indexOf(this.bjpID) !== -1 || e[e.length - 1].indexOf(this.hzpID) !== -1)) {
        this.model.approvalNo = ""
        if (this.model.spuCategory === 4) {
          this.model.approvalNo = this.initializedSpuModel.approvalNo
        }
      }
      if (this._businessScopeList && this._businessScopeList.length < e.length) {
        let last = [],
          arr = JSON.parse(JSON.stringify(this.model.businessScopeList))
        for (let list of e) {
          for (let item of this._businessScopeList) {
            if (item.join(",") == list.join(",")) {
              last = []
              break
            } else {
              last = list
            }
          }
          if (last.length) break
        }
        e.forEach((item, index) => {
          if (item[item.length - 2] == last[last.length - 2] && item[item.length - 1] != last[last.length - 1] && last.length > 0) {
            arr.splice(index, 1)
            this.model.businessScopeList = []
            this.model.businessScopeList = arr
            return
          }
        })
      }
      this._businessScopeList = JSON.parse(JSON.stringify(this.model.businessScopeList))
      this.$emit("isShowBtn", e, this.model.spuCategory, this.bjpID)
      this.editRateFunction()
      if(this.$route.query.pageType != 'detail'){
        this.autoFillInfo(this.model.spuCategory, '', 'NO_CHANGE_CATEGORY')
      }
    },
    // `type`  '1:商品大类为非药&经营范围包含保健食品 2: 商品大类为医疗器械3.当商品大类=普通药品时，批准文号校验规则',
    //  `business_scope` '经营范围:非药->化妆品:1,食品->保健食品:2',
    validateApprovalNo10(rule, value, callback) {
      var regInfo = this.regInfo.filter((item) => {
        return item.type == 1 && item.businessScope == 1
      })
      this.validateApprovalNo(rule, value, callback, regInfo[0].regList)
    },
    validateApprovalNo1(rule, value, callback) {
      var regInfo = this.regInfo.filter((item) => {
        return item.type == 1 && item.businessScope == 2
      })
      this.validateApprovalNo(rule, value, callback, regInfo[0].regList)
    },

    validateApprovalNo2(rule, value, callback) {
      // 医疗器械校验
      if (value.indexOf(",") != -1 || value.indexOf("+") != -1 || value.indexOf("/") != -1) {
        let regInfo = this.regInfo.filter((item) => {
          return item.type == 2 && item.businessScope === null
        })
        this.validateApprovalNo(rule, value, callback, regInfo[0].regList) // 旧的校验
      } else {
        // 把所选的经营范围id放到一个数组
        let businessScopeList = []
        this.model.businessScopeList.forEach((item) => {
          item.forEach((e) => {
            businessScopeList.push(e)
          })
        })
        // 找到对应 businessScope 的校验规则，和 === null的为之前默认的校验规则
        let regInfo = this.regInfo.filter((item) => {
          return (item.type == 2 && businessScopeList.find((e) => e === item.businessScope)) || (item.type == 2 && item.businessScope === null)
        })
        this.validateApprovalNoNew(rule, value, callback, regInfo) // 新的校验
      }
    },

    validateApprovalNo3(rule, value, callback) {
      var regInfo = this.regInfo.filter((item) => {
        return item.type == 3
      })
      this.validateApprovalNo(rule, value, callback, regInfo[0].regList)
    },

    validateApprovalNo(rule, value, callback, regList) {
      let arr = []
      let flag = false
      if (value != "") {
        if (value.indexOf(",") != -1) {
          arr = value.split(",")
        } else if (value.indexOf("+") != -1) {
          arr = value.split("+")
        } else {
          arr.push(value)
        }
      }
      let isRepeatFlag = false
      isRepeatFlag = isRepeat(arr)
      if (isRepeatFlag) {
        callback(new Error("批准文号重复"))
      } else {
        arr.forEach((item) => {
          let result = []
          regList.forEach((reg) => {
            var regexp = new RegExp(reg)
            result.push(regexp.test(item))
          })
          if (result.findIndex((val) => val) === -1) {
            flag = true
            return
          }
        })

        if (value === "") {
          callback(new Error("请输入批准文号"))
        } else if (flag) {
          callback(new Error("请输入正确的批准文号"))
        } else if (isRepeatFlag) {
        } else {
          callback()
        }
      }
    },
    validateApprovalNoNew(rule, value, callback, regInfo) {
      let arr = []
      let flag = false
      if (value != "") {
        if (value.indexOf(",") != -1) {
          arr = value.split(",")
        } else if (value.indexOf("+") != -1) {
          arr = value.split("+")
        } else if (value.indexOf("/") != -1) {
          arr = value.split("/")
        } else {
          arr.push(value)
        }
      }
      let isRepeatFlag = false
      isRepeatFlag = isRepeat(arr)
      if (isRepeatFlag) {
        callback(new Error("批准文号重复"))
      } else {
        arr.forEach((item) => {
          let result = []

          regInfo.forEach((regList) => {
            let testResult = []
            regList.regList.forEach((reg) => {
              var regexp = new RegExp(reg)
              testResult.push(regexp.test(item))
            })
            result.push(testResult)
          })

          result.forEach((item) => {
            if (item.findIndex((val) => val) === -1) {
              flag = true
              // return;
            }
          })
        })

        if (value === "") {
          callback(new Error("请输入批准文号"))
        } else if (flag) {
          callback(new Error("请输入正确的批准文号"))
        } else if (isRepeatFlag) {
        } else {
          callback()
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.basic-info-container {
  width: 100%;
  .title-tips {
    color: red;
    font-size: 12px;
    margin-left: 5px;
  }
  .basic-info-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
  }
  .basic-info-form {
    padding-right: 40px;
    .el-row {
      flex-wrap: wrap;
      .el-col {
        display: flex;
        justify-content: flex-start;
        .el-form-item.is-error /deep/ .el-radio__inner {
          border-color: #f56c6c;
        }
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;

          /deep/ {
            .el-form-item__label {
              width: 130px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;

              &:before {
                margin-right: 0 !important;
              }
            }

            .el-form-item__content {
              flex: 1;
            }
            .el-radio__inner {
              width: 18px;
              height: 18px;
            }
          }
          .approvalImgBtn {
            background: #fff;
            color: #3b95a8;
          }
          .approvalImgInput {
            text-align: center;
          }
          .el-icon-more {
            cursor: pointer;
          }
          .el-radio {
            line-height: 25px;
          }
          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
        .is-change {
          /deep/ {
            // 调整修改项lable样式
            .el-form-item__label {
              color: #f56c6c;
              font-weight: bold;
            }
          }
        }
        .is-required {
          /deep/ {
            // 必填项label样式
            .el-form-item__label {
              // color: #3B95A8;
              // font-weight: bold;
            }
          }
        }
        .info-suffix-icon {
          margin: 12px 0 0 5px;
          color: #77787e;
          cursor: pointer;
        }

        &.reset-search-button {
          /deep/ {
            .el-input-group__append {
              border-radius: 0 4px 4px 0;
              padding: 0 16px;
              background-color: #ffffff;
              overflow: hidden;
            }
          }
        }

        &.reset-input-with-select {
          /deep/ {
            .el-select .el-input {
              width: 66px;
            }
          }
        }

        &.reset-checkbox-group {
          .el-checkbox-group {
            min-height: 40px;
            margin-top: 3px;
            .el-checkbox {
              margin-right: 15px;
            }
          }
        }
      }
    }
  }
  .fix-select-arrow {
    width: 40px;
    height: 100%;
    position: absolute;
    right: 0;
    z-index: 999;
    cursor: pointer;
  }
  .approvalImgBox {
    max-height: 50vh;
    overflow-y: scroll;
  }

  .manufacturer-select-container{
    width: 100%;
    display: flex;
    align-items: center;
    .manufacturer-select {
      flex: 1;
      margin-right: 10px;
    }
  }
  .copy-input-container{
    width: 100%;
    display: flex;
    align-items: center;
    .copy-input {
      flex: 1;
      margin-right: 10px;
    }
  }
  .copy-btn{
    margin-right: 10px;

    .copy-btn-below{
      color: #4A95A9;
      cursor: pointer;
    }
  }
}
/**
   * 查询表单
   */
.search-form-wrap {
  width: 100%;
  padding: 15px;
  max-height: calc(100vh - 77px);
  border-bottom: 1px dashed #e4e4eb;
  overflow-y: scroll;
  .el-row {
    flex-wrap: wrap;

    .el-col {
      display: flex;
      justify-content: flex-end;

      .el-form-item {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;

        /deep/ {
          .el-form-item__label {
            width: 96px;
            line-height: normal;
            padding: 0 12px;
            color: #292933;
            font-weight: normal;
          }
          .el-form-item__content {
            flex: 1;
          }
        }
        .el-cascader {
          width: 100%;
        }
        .el-input {
          width: 100%;
        }

        .el-select {
          width: 100%;
        }
      }
    }
  }
}
/**
   * table
   */
.table-wrap {
  width: 100%;
  min-height: 120px;
  height: calc(100vh - 430px);
  // padding: 0 15px;
}
.my-vxe-table .vxe-body--row.row--current {
  background-color: #000;
  color: #fff;
}
/**
   * 分页样式
   */
.page-size-custom {
  font-size: 0;
  margin-left: 5px;
  font-size: 0;

  .el-input {
    width: 80px;
    height: 28px;
  }

  .tips {
    margin: 0 5px;
    font-size: 13px;
    font-weight: normal;
    color: #606266;
  }

  .el-button {
    background-color: #3b95a8;
    border-color: #3b95a8;

    &:hover {
      background-color: #62aab9;
      border-color: #62aab9;
      color: #fff;
    }

    &.active {
      background-color: #358697 !important;
      border-color: #358697 !important;
      color: #fff !important;
      outline: none;
    }

    &:focus {
      background-color: #62aab9;
      border-color: #62aab9;
      color: #fff;
    }
  }
}
.packageCode-wrap {
  display: flex;
  margin-bottom: 20px;
  .packageCode-title {
    width: 130px;
    padding: 0 12px;
    text-align: right;
    font-size: 14px;
  }
  .packageCode-content {
    flex: 1;
    padding: 0 20px;
    background: #f0f2f5;
  }
}
</style>
lor: #358697 !important;
      border-color: #358697 !important;
      color: #fff !important;
      outline: none;
    }

    &:focus {
      background-color: #62aab9;
      border-color: #62aab9;
      color: #fff;
    }
  }
}
.packageCode-wrap {
  display: flex;
  margin-bottom: 20px;
  .packageCode-title {
    width: 130px;
    padding: 0 12px;
    text-align: right;
    font-size: 14px;
  }
  .packageCode-content {
    flex: 1;
    padding: 0 20px;
    background: #f0f2f5;
  }
}

</style>
