import { getTotalDictionaryTree, categoryList, dictSearchTypeAndName } from "@/api/dict"
import { getId } from "@/utils/index.js"
import { findProductSale } from "@/api/product.js";
import { dictListSearch } from "@/api/workManage"
// import { MessageBox, Message } from 'element-ui'
let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
const state = {
  // 商品全局问号数据
  info: [],
  // 商品全局共用下拉框option数据
  selectOptions: cacheDist || {},
  // TYPEENUM: {
  //   EMPTY: "", //未选择
  //   GIFT: 6, //赠品
  //   NOT_MEDICINE: 5, //非药
  //   MEDICAL_INSTRUMENT: 4, //医疗器械
  //   TRADITIONAL_MEDICINE: 3, //中药
  //   GENERAL_MEDICINE: "GENERAL_MEDICINE" //普通药品
  // },
  // 商品分类
  spuCategory: {
    type: "EMPTY",
    name: "",
    id: "",
  },
  spuApprovalNo: "",
  spuBusinessScopeListType: "",
  // 商品操作类型
  /**
   * 操作类型
   * 新增 :add
   * 草稿：draft
   * 详情 :detail
   * 修改："edit"
   * 一审：auditLevel1
   * 二审：auditLevel2
   * 复用：reuse
   * 同步：update
   * 预首营：operate
   * 驳回修改：RejectEdit
   * 新品上报：present
   */
  operationType: "",
  productSaleInfo: [],
  skuForm: {},
  firstCategory: -1,
  unitTargetFirstCategory: false,
  unitZSYYFirstCategory: false,
  isChangeCategory: '',
}

const mutations = {
  // 设置问号数据
  SET_INFO: (state, info) => {
    state.info = info
  },
  // 设置选项数据
  SET_SELECT_OPTIONS: (state, options) => {
    state.selectOptions = options;
  },
  // 设置商品分类
  SET_SPU_CATEGOTY: (state, value) => {
    state.spuCategory = value;
  },
  // 设置商品操作类型
  SET_OPERATION_TYPE: (state, value) => {
    state.operationType = value;
  },
  SET_PRODUCTSALEINFO: (state, value) => {
    state.productSaleInfo = value
  },
  SET_SKUFORM: (state, value) => {
    state.skuForm = value
  },
  SET_FIRSTCATEGORY: (state, value) => {
    state.firstCategory = value
  },
  SET_UNIT_TARGET_CATEGORY: (state, value) => {
    state.unitTargetFirstCategory = value
  },
  SET_UNIT_ZSYY_CATEGORY: (state, value) => {
    state.unitZSYYFirstCategory = value
  },
  SET_SPU_APPROVAL_NO: (state, value) => {
    state.spuApprovalNo = value
  },
  SET_SPU_BUSINESS_SCOPE_LIST_TYPE: (state, value) => {
    state.spuBusinessScopeListType = value
  },
  SET_GENERAL_NAME: (state, value) => {
    state.generalName = value
  },
  SET_SPEC: (state, value) => {
    state.spec = value
  },
  SET_IS_CHANGE_CATEGORY: (state, value) => {
    state.isChangeCategory = value
  }
}

const actions = {
  // 商品文号文本
  async getInfo({ commit, state }) {
    let info = await dictListSearch({
      type: 2,
    })
    commit('SET_INFO', info.data.list)
    localStorage.setItem("info", JSON.stringify(info))
  },
  async getOptions({ commit, state }) {
    // 商品分类选项
    dictListSearch({
      type: 7,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        spuCategoryOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        spuCategoryOptions: Object.freeze(res.data.list)
      }))
    })
    // 商品大类选项
    dictListSearch({
      type: 19,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        largeCategoryOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        largeCategoryOptions: Object.freeze(res.data.list)
      }))
    })

    // 存储条件
    await dictListSearch({
      type: 6,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        storageCondOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        storageCondOptions: Object.freeze(res.data.list)
      }))
    })

    // 一级分类
    categoryList({
      isValid: "1",
      level: 1,
      parentId: ""
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        firstCategoryOptions: Object.freeze(res.data)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        firstCategoryOptions: Object.freeze(res.data)
      }))
    })

    // 包装单位
    dictListSearch({
      type: 10,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        packageUnitOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        packageUnitOptions: Object.freeze(res.data.list)
      }))
    })

    // 品牌分类
    dictListSearch({
      type: 24,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        brandCategoryOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        brandCategoryOptions: Object.freeze(res.data.list)
      }))
    })

    // 特殊属性
    dictListSearch({
      type: 13,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        specialAttrListOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        specialAttrListOptions: Object.freeze(res.data.list)
      }))
    })

    // 剂型
    dictListSearch({
      type: 5,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        dosageFormOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        dosageFormOptions: Object.freeze(res.data.list)
      }))
    })

    // 处方分类
    dictListSearch({
      type: 9,
    }).then(res => {
      // res.data.list.insert(0, {"dictName":"空","id":999999,"isValid":1,"showContent":"","type":"9"})
      // res.data.list.push({"dictName":"空","id":999999,"isValid":1,"showContent":"","type":"9"})

      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        prescriptionCategoryOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        prescriptionCategoryOptions: Object.freeze(res.data.list)
      }))
    })

    // 税率
    dictListSearch({
      type: 8,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        inRateOptions: Object.freeze(res.data.list),
        outRateOptions: Object.freeze(res.data.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        inRateOptions: Object.freeze(res.data.list),
        outRateOptions: Object.freeze(res.data.list)
      }))
    })

    // 生产厂家初始数据
    dictSearchTypeAndName({
      dictName: "药",
      type: 12,
    }).then(res => {
      commit('SET_SELECT_OPTIONS', {
        ...state.selectOptions,
        distManufacturerOptions: Object.freeze(res.list)
      });
      let cacheDist = JSON.parse(localStorage.getItem("cacheDist"));
      localStorage.setItem("cacheDist", JSON.stringify({
        ...cacheDist,
        distManufacturerOptions: Object.freeze(res.list)
      }))
    })

    // // 所属经营范围
    // let businessScopeListOptions = await getTotalDictionaryTree({
    //  type:"11",
    // })
    // let businessScopeListOptionsParentId=[];
    // getId(businessScopeListOptions.data,businessScopeListOptionsParentId);

    // let optionsObj = {
    //   // 生产厂家初始数据
    //   distManufacturerOptions: Object.freeze(distManufacturer.list),
    //   // 商品分类
    //   spuCategoryOptions: Object.freeze(spuCategoryOptions.data.list),
    //   // 商品大类
    //   largeCategoryOptions: Object.freeze(largeCategoryOptions.data.list),
    //   // 所属经营范围 （全部）
    //   // businessScopeListOptions:Object.freeze(businessScopeListOptions.data),
    //   // // 所属经营范围父级ID
    //   // businessScopeListOptionsParentId,
    //   // 存储条件
    //   storageCondOptions: Object.freeze(storageCondOptions.data.list),
    //   // 一级分类
    //   firstCategoryOptions: Object.freeze(firstCategoryOptions.data),
    //   // 包装单位
    //   packageUnitOptions: Object.freeze(packageUnitOptions.data.list),
    //   // 品牌分类
    //   brandCategoryOptions: Object.freeze(brandCategoryOptions.data.list),
    //   // 特殊属性
    //   specialAttrListOptions: Object.freeze(specialAttrListOptions.data.list),
    //   // 剂型
    //   dosageFormOptions: Object.freeze(dosageFormOptions.data.list),
    //   // 处方分类
    //   prescriptionCategoryOptions: Object.freeze(prescriptionCategoryOptions.data.list),
    //   // 税率
    //   inRateOptions: Object.freeze(TaxRateHtml.data.list),
    //   outRateOptions: Object.freeze(TaxRateHtml.data.list),
    // }
    // commit('SET_SELECT_OPTIONS', optionsObj);
    // 缓存字典
  },
  // 获取精装图版本
  getSaleInfoAction({ commit }, data) {
    findProductSale(data).then((res) => {
      res.forEach(item => {
        item.checked = false;
        item.disabled = false;
      })
      commit('SET_PRODUCTSALEINFO', res);
    });
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
