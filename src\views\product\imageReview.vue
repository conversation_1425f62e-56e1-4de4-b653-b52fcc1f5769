<template>
  <div class="image-review-container">
    <!-- 图片审核标题区域 -->
    <div class="image-audit-section">
      <div class="audit-header" @click="toggleAuditSection">
        <span class="audit-title">图片审核</span>
        <el-tooltip content="此处为新品上报的图片审核流程，此处图片审核完，不会流转至原独立的图片待审核列表。请务必按照正常的图片审核要求进行审核。" placement="top">
          <i class="el-icon-question audit-help-icon" @click.stop></i>
        </el-tooltip>
        <i :class="auditSectionExpanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" class="audit-toggle-icon"></i>
      </div>

      <el-collapse-transition>
        <div v-show="auditSectionExpanded" class="audit-content">
          <!-- 主图区域 -->
          <div class="main-image-section image-section">
            <div class="image-title">
              主图
              <br /><span class="image-limit">(最多1张)</span>
            </div>
            <div class="image-content">
              <DraggableImageUpload ref="mainImageUpload" :fileList="mainImageList" :limit="1" :uploadUrl="uploadUrl"
                :enableOCR="enableOCR" :disabled="formDisable" :enableDrag="enableDrag" :dragGroup="'images'"
                :dragPutValidator="canPutToMain" :globalOcrManager="this" @change="handleMainImageChange"
                @dragChange="handleDragChange" :preview="true" class="main-drag-area" data-container="main" />
            </div>
          </div>

          <!-- 外包装图片区域 -->
          <div class="package-image-section image-section">
            <div class="image-title">
              外包装图片
              <br /><span class="image-limit">(最多60张)</span>
            </div>
            <div class="image-content">
              <DraggableImageUpload ref="packageImageUpload" :fileList="packageImageList" :limit="60"
                :uploadUrl="uploadUrl" :enableOCR="enableOCR" :disabled="formDisable" :enableDrag="enableDrag"
                :dragGroup="'images'" :globalOcrManager="this" @change="handlePackageImageChange"
                @dragChange="handleDragChange" :preview="true" class="package-drag-area" data-container="package" />
            </div>
          </div>

          <!-- 说明书图片区域 -->
          <div class="instruction-image-section image-section">
            <div class="image-title">
              说明书图片
              <br /><span class="image-limit">(最多60张)</span>
            </div>
            <div class="image-content">
              <DraggableImageUpload ref="instructionImageUpload" :fileList="instructionImageList" :limit="60"
                :uploadUrl="uploadUrl" :enableOCR="enableOCR" :disabled="formDisable" :enableDrag="enableDrag"
                :dragGroup="'images'" :globalOcrManager="this" @change="handleInstructionImageChange"
                @dragChange="handleDragChange" :preview="true" class="instruction-drag-area"
                data-container="instruction" />
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 渠道图片使用控制 -->
    <div class="channel-image-controls">
      <el-form
        ref="channelImageForm"
        :model="formData"
        :rules="formRules"
        label-width="230px"
        :disabled="formDisable">
        <el-form-item
          label="是否使用渠道上传图片"
          prop="useChannelImages"
          required>
          <el-radio-group
            v-model="useChannelImages"
            :disabled="formDisable"
            @change="handleUseChannelImagesChange">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="图片质量"
          prop="imageQuality"
          required>
          <el-radio-group
            v-model="imageQuality"
            :disabled="formDisable"
            @change="handleImageQualityChange">
            <el-radio label="needs_design">需设计精修</el-radio>
            <el-radio label="direct_use">可直接使用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import DraggableImageUpload from "./components/DraggableImageUpload.vue";

export default {
  name: "ImageReview",
  components: {
    DraggableImageUpload,
  },
  props: {
    // 外包装图片数据（用于初始化）
    outPackageImgList: {
      type: Array,
      default: () => [],
    },
    // 说明书图片数据（用于初始化）
    directionImgList: {
      type: Array,
      default: () => [],
    },
    // 是否禁用表单
    formDisable: {
      type: Boolean,
      default: false,
    },
    // 是否启用OCR功能
    enableOCR: {
      type: Boolean,
      default: true,
    },
    // 是否启用拖拽功能
    enableDrag: {
      type: Boolean,
      default: true,
    },
    // 渠道图片使用状态（用于初始化）
    initialUseChannelImages: {
      type: Boolean,
      default: null, // null 表示使用组件内部默认值
    },
    // 图片质量选择（用于初始化）
    initialImageQuality: {
      type: String,
      default: null, // null 表示使用组件内部默认值
      validator: function (value) {
        return value === null || ['needs_design', 'direct_use'].includes(value);
      }
    },
  },
  data() {
    return {
      // 图片审核区域展开状态
      auditSectionExpanded: true,

      // 图片数据
      mainImageList: [],      // 主图（限制1张）
      packageImageList: [],   // 外包装图片
      instructionImageList: [], // 说明书图片

      // 渠道图片控制
      useChannelImages: true,
      imageQuality: 'needs_design',

      // 内部操作标志位，防止重复初始化
      isInternalOperation: false,

      // 初始化标志位，防止重复初始化
      hasInitialized: false,

      // 手动添加图片标志位，区分手动添加和初始化数据
      hasManuallyAddedImages: false,

      // 上传配置
      uploadUrl: process.env.VUE_APP_BASE_API + "/api/file/upload/fileAndName",

      // 全局 OCR 数据管理 - 跨容器共享
      globalOcrResults: {},

      // 拖拽滚动管理器
      dragScrollManager: {
        isActive: false,
        scrollTimer: null,
        scrollSpeed: 20, // 提高基础滚动速度
        triggerZone: 150, // 扩大触发区域从80px到150px
        lastMouseY: 0,
        scrollContainer: null,
        throttleTimer: null,
        // 配置选项
        config: {
          minScrollSpeed: 8, // 提高最小滚动速度
          maxScrollSpeed: 40, // 提高最大滚动速度
          throttleDelay: 12, // 降低延迟，提高响应速度（约83fps）
          smoothScrolling: false, // 拖拽时使用即时滚动
          enableVisualFeedback: true,
          // 响应式触发区域配置
          responsiveTriggerZones: {
            small: 120,  // 小屏幕（≤1366px）
            medium: 150, // 中等屏幕（1367-1919px）
            large: 180   // 大屏幕（≥1920px）
          },
          // 加速度配置
          acceleration: {
            enabled: true,
            factor: 1.5, // 加速倍数
            threshold: 0.7 // 加速触发阈值（距离边缘的比例）
          }
        }
      },

      // 表单校验规则
      formRules: {
        useChannelImages: [
          {
            required: true,
            message: '请选择是否使用渠道上传图片',
            trigger: 'change'
          }
        ],
        imageQuality: [
          {
            required: true,
            message: '请选择图片质量',
            trigger: 'change'
          }
        ]
      },
    };
  },
  computed: {
    // 表单数据对象，用于表单验证
    formData() {
      return {
        useChannelImages: this.useChannelImages,
        imageQuality: this.imageQuality,
      };
    },
  },
  watch: {
    // 监听外包装图片数据变化，用于初始化
    outPackageImgList: {
      handler(newVal) {
        // 重置手动添加标志位，允许重新初始化
        if (newVal && newVal.length > 0) {
          this.hasManuallyAddedImages = false;
        }
        this.initializeImageData();
      },
      immediate: true,
      deep: true,
    },
    // 监听说明书图片数据变化
    directionImgList: {
      handler(newVal) {
        // 重置手动添加标志位，允许重新初始化
        if (newVal && newVal.length > 0) {
          this.hasManuallyAddedImages = false;
        }
        this.initializeImageData();
      },
      immediate: true,
      deep: true,
    },
    // 监听渠道图片使用状态变化（保留用于外部 props 变化）
    useChannelImages: {
      handler(newVal, oldVal) {
        // 只处理非用户交互的变化（如外部 props 变化）
        if (!this.isInternalOperation && newVal !== oldVal) {
          // 触发表单验证
          this.$nextTick(() => {
            if (this.$refs.channelImageForm) {
              this.$refs.channelImageForm.validateField('useChannelImages');
            }
          });
        }
      },
    },
    // 监听图片质量选择变化（保留用于外部 props 变化）
    imageQuality: {
      handler(newVal, oldVal) {
        // 只处理非用户交互的变化（如外部 props 变化）
        if (!this.isInternalOperation && newVal !== oldVal) {
          // 触发表单验证
          this.$nextTick(() => {
            if (this.$refs.channelImageForm) {
              this.$refs.channelImageForm.validateField('imageQuality');
            }
          });
        }
      },
    },

    // 监听外部传入的渠道图片使用状态变化
    initialUseChannelImages: {
      handler(newVal) {
        if (newVal !== null && newVal !== this.useChannelImages) {
          this.isInternalOperation = true;
          this.useChannelImages = newVal;
          this.emitDataChange();
          this.$nextTick(() => {
            this.isInternalOperation = false;
          });
        }
      },
      immediate: true,
    },

    // 监听外部传入的图片质量选择变化
    initialImageQuality: {
      handler(newVal) {
        if (newVal !== null && newVal !== this.imageQuality) {
          this.isInternalOperation = true;
          this.imageQuality = newVal;
          this.emitDataChange();
          this.$nextTick(() => {
            this.isInternalOperation = false;
          });
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.initializeImageData();

    // 延迟初始化 OCR 数据，确保所有组件都已挂载
    this.$nextTick(() => {
      setTimeout(() => {
        this.refreshAllContainersOcrData();
      }, 500);
    });

    // 初始化拖拽滚动功能
    this.initializeDragScroll();

    // 开发环境下添加全局测试方法
    if (process.env.NODE_ENV === 'development') {
      window.testDragScroll = () => {
        console.log('[DragScroll] 全局测试方法被调用');
        this.testScrollFunctionality();
      };

      window.testScrollUp = () => {
        console.log('[DragScroll] 测试向上滚动');
        this.performScroll(-1, 100, true);
      };

      window.testScrollDown = () => {
        console.log('[DragScroll] 测试向下滚动');
        this.performScroll(1, 100, true);
      };

      console.log('[DragScroll] 全局测试方法已注册: testDragScroll(), testScrollUp(), testScrollDown()');
    }
  },

  beforeDestroy() {
    // 清理拖拽滚动相关的事件监听
    this.cleanupDragScroll();
  },
  methods: {
    /**
     * 初始化图片数据
     * 将外包装图片第一张设置为主图，剩余的保留在外包装区域
     */
    initializeImageData() {
      // 防止在内部操作（如拖拽）时重复初始化
      if (this.isInternalOperation) {
        return;
      }

      // 标记为内部操作，防止触发其他监听器
      this.isInternalOperation = true;

      // 处理外包装图片
      if (this.outPackageImgList && this.outPackageImgList.length > 0) {
        // 第一张图片作为主图
        this.mainImageList = [this.formatImageData(this.outPackageImgList[0])];
        // 剩余图片保留在外包装区域
        this.packageImageList = this.outPackageImgList.slice(1).map(img => this.formatImageData(img));
      } else {
        // 只有在没有手动添加图片的情况下才清空
        if (!this.hasManuallyAddedImages) {
          this.mainImageList = [];
          this.packageImageList = [];
        }
      }

      // 处理说明书图片
      if (this.directionImgList && this.directionImgList.length > 0) {
        this.instructionImageList = this.directionImgList.map(img => this.formatImageData(img));
      } else {
        // 只有在没有手动添加图片的情况下才清空
        if (!this.hasManuallyAddedImages) {
          this.instructionImageList = [];
        }
      }

      // 初始化渠道图片控制数据
      this.initializeControlData();

      // 标记已经初始化过
      this.hasInitialized = true;

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 初始化控制数据（渠道图片使用状态和图片质量）
     */
    initializeControlData() {
      // 只在首次初始化或有外部数据传入时更新
      if (!this.hasInitialized || this.initialUseChannelImages !== null || this.initialImageQuality !== null) {

        // 初始化渠道图片使用状态
        if (this.initialUseChannelImages !== null) {
          this.useChannelImages = this.initialUseChannelImages;
        }

        // 初始化图片质量选择
        if (this.initialImageQuality !== null) {
          this.imageQuality = this.initialImageQuality;
        }
      }
    },

    /**
     * 格式化图片数据，确保符合 ImageUploadWithOCR 组件的要求
     */
    formatImageData(imageData) {
      return {
        uid: imageData.uid || imageData.mediaUrl || imageData.url,
        name: imageData.name || imageData.mediaName || `image-${Date.now()}`,
        mediaName: imageData.mediaName || imageData.name || `image-${Date.now()}`,
        mediaUrl: imageData.mediaUrl || imageData.url,
        url: imageData.url || imageData.mediaUrl,
        status: imageData.status || 'success',
        response: imageData.response,
      };
    },

    /**
     * 切换图片审核区域的展开/折叠状态
     */
    toggleAuditSection(event) {
      // 阻止事件冒泡，防止意外触发其他事件
      if (event) {
        event.stopPropagation();
      }

      // 设置内部操作标志位
      this.isInternalOperation = true;
      this.auditSectionExpanded = !this.auditSectionExpanded;

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 主图变化处理
     */
    handleMainImageChange(imageList) {

      this.isInternalOperation = true;
      this.hasManuallyAddedImages = true; // 标记为手动操作
      this.mainImageList = imageList;
      this.emitDataChange();

      // 如果容器变为空，确保相关 UI 状态更新
      if (imageList.length === 0) {
        this.$nextTick(() => {
          if (this.$refs.mainImageUpload) {
            this.$refs.mainImageUpload.$forceUpdate();
          }
        });
      }

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 外包装图片变化处理
     */
    handlePackageImageChange(imageList) {

      this.isInternalOperation = true;
      this.hasManuallyAddedImages = true; // 标记为手动操作
      this.packageImageList = imageList;
      this.emitDataChange();

      // 如果容器变为空，确保相关 UI 状态更新
      if (imageList.length === 0) {
        this.$nextTick(() => {
          if (this.$refs.packageImageUpload) {
            this.$refs.packageImageUpload.$forceUpdate();
          }
        });
      }

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 说明书图片变化处理
     */
    handleInstructionImageChange(imageList) {

      this.isInternalOperation = true;
      this.hasManuallyAddedImages = true; // 标记为手动操作
      this.instructionImageList = imageList;
      this.emitDataChange();

      // 如果容器变为空，确保相关 UI 状态更新
      if (imageList.length === 0) {
        this.$nextTick(() => {
          if (this.$refs.instructionImageUpload) {
            this.$refs.instructionImageUpload.$forceUpdate();
          }
        });
      }

      this.$nextTick(() => {
        this.isInternalOperation = false;
      });
    },

    /**
     * 向父组件发送数据变化事件
     */
    emitDataChange() {
      const data = {
        mainImageList: this.mainImageList,
        packageImageList: this.packageImageList,
        instructionImageList: this.instructionImageList,
        useChannelImages: this.useChannelImages,
        imageQuality: this.imageQuality,
      };

      this.$emit('change', data);
    },

    /**
     * 获取所有图片数据（供父组件调用）
     */
    getImageData() {
      const data = {
        mainImageList: this.mainImageList || [],
        packageImageList: this.packageImageList || [],
        instructionImageList: this.instructionImageList || [],
        useChannelImages: this.useChannelImages,
        imageQuality: this.imageQuality,
        // 合并后的外包装图片列表（主图 + 外包装图片）
        combinedPackageList: [...(this.mainImageList || []), ...(this.packageImageList || [])],
        // 说明书图片列表
        directionImgList: this.instructionImageList || [],
      };

      // 只在数据异常时输出警告
      if (data.combinedPackageList.length === 0 && (this.mainImageList.length > 0 || this.packageImageList.length > 0)) {
        console.warn('[ImageReview] ⚠️ 图片数据异常：主图或外包装图片存在但combinedPackageList为空');
      }

      return data;
    },

    /**
     * 拖拽变化处理
     */
    handleDragChange() {
      // 设置内部操作标志位
      this.isInternalOperation = true;
      // 拖拽完成后触发数据变化事件
      this.$nextTick(() => {
        this.emitDataChange();
        this.isInternalOperation = false;
      });
    },

    /**
     * 判断是否可以拖拽到主图区域
     * 主图区域最多只能有一张图片
     */
    canPutToMain(to, from, dragEl, evt) {
      const canPut = this.mainImageList.length < 1;
      return canPut;
    },

    /**
     * 重置初始化状态，允许重新初始化数据
     * 可以从外部调用此方法来强制重新初始化
     */
    resetInitializationState() {
      this.hasInitialized = false;
      this.hasManuallyAddedImages = false;
      this.initializeImageData();
    },

    /**
     * 处理渠道图片使用状态变化
     */
    handleUseChannelImagesChange(value) {

      // 触发表单验证
      this.$nextTick(() => {
        if (this.$refs.channelImageForm) {
          this.$refs.channelImageForm.validateField('useChannelImages');
        }
      });

      // 触发数据变化事件（原有逻辑保持不变）
      if (!this.isInternalOperation) {
        this.isInternalOperation = true;
        this.emitDataChange();
        this.$nextTick(() => {
          this.isInternalOperation = false;
        });
      }
    },

    /**
     * 处理图片质量选择变化
     */
    handleImageQualityChange(value) {

      // 触发表单验证
      this.$nextTick(() => {
        if (this.$refs.channelImageForm) {
          this.$refs.channelImageForm.validateField('imageQuality');
        }
      });

      // 触发数据变化事件（原有逻辑保持不变）
      if (!this.isInternalOperation) {
        this.isInternalOperation = true;
        this.emitDataChange();
        this.$nextTick(() => {
          this.isInternalOperation = false;
        });
      }
    },

    /**
     * 验证渠道图片控制表单
     * @returns {Promise<boolean>} 验证结果
     */
    validateChannelImageForm() {
      return new Promise((resolve) => {
        if (!this.$refs.channelImageForm) {
          console.warn('[ImageReview] Channel image form ref not found');
          resolve(true); // 如果表单引用不存在，默认通过验证
          return;
        }

        this.$refs.channelImageForm.validate((valid, fields) => {
          if (valid) {
            resolve(true);
          } else {

            // 显示第一个验证错误
            const firstErrorField = Object.keys(fields)[0];
            const firstError = fields[firstErrorField][0];
            this.$message.error(firstError.message);

            resolve(false);
          }
        });
      });
    },

    /**
     * 重置渠道图片控制表单验证状态
     */
    resetChannelImageFormValidation() {
      if (this.$refs.channelImageForm) {
        this.$refs.channelImageForm.resetFields();
      }
    },

    /**
     * 清除渠道图片控制表单验证状态
     */
    clearChannelImageFormValidation() {
      if (this.$refs.channelImageForm) {
        this.$refs.channelImageForm.clearValidate();
      }
    },

    /**
     * 获取全局 OCR 数据
     */
    getGlobalOcrData(file) {
      const key = file.uid || file.mediaUrl || file.url;
      const data = this.globalOcrResults[key] || null;
      return data;
    },

    /**
     * 设置全局 OCR 数据
     */
    setGlobalOcrData(file, ocrData) {
      const key = file.uid || file.mediaUrl || file.url;

      this.$set(this.globalOcrResults, key, {
        loading: ocrData.loading,
        extracted: ocrData.extracted,
        fields: [...(ocrData.fields || [])]
      });

      // 通知所有组件刷新 OCR 数据
      this.$nextTick(() => {
        this.notifyOcrDataUpdate(file);
      });
    },

    /**
     * 删除全局 OCR 数据
     */
    removeGlobalOcrData(file) {
      const key = file.uid || file.mediaUrl || file.url;

      if (this.globalOcrResults[key]) {
        this.$delete(this.globalOcrResults, key);

        // 验证删除结果
        const afterDeletion = this.globalOcrResults[key];
      } else {
        console.log('[GlobalOCR] No OCR data found to remove for file:', file.name);
      }
    },

    /**
     * 通知所有组件 OCR 数据已更新
     */
    notifyOcrDataUpdate(file) {

      // 通知所有 DraggableImageUpload 组件刷新指定文件的 OCR 数据
      const components = [
        this.$refs.mainImageUpload,
        this.$refs.packageImageUpload,
        this.$refs.instructionImageUpload
      ].filter(comp => comp);

      components.forEach(comp => {
        if (comp && typeof comp.refreshOcrDataForFile === 'function') {
          comp.refreshOcrDataForFile(file);
        }
      });
    },

    /**
     * 通知所有组件 OCR 数据已删除
     */
    notifyOcrDataDeletion(file) {

      // 通知所有 DraggableImageUpload 组件清理指定文件的 OCR 数据
      const components = [
        this.$refs.mainImageUpload,
        this.$refs.packageImageUpload,
        this.$refs.instructionImageUpload
      ].filter(comp => comp);

      components.forEach(comp => {
        if (comp && typeof comp.clearOcrDataForFile === 'function') {
          comp.clearOcrDataForFile(file);
        }
      });

      // 强制刷新所有容器
      this.$nextTick(() => {
        this.refreshAllContainersOcrData();
      });
    },

    /**
     * 处理拖拽变化 - 管理 OCR 数据迁移
     */
    handleDragChange(evt) {

      // 如果是添加事件，强制刷新目标容器的 OCR 数据
      if (evt.added && evt.added.element) {
        const file = evt.added.element;

        // 延迟刷新，确保 DOM 更新完成
        this.$nextTick(() => {
          setTimeout(() => {
            this.notifyOcrDataUpdate(file);
            this.refreshAllContainersOcrData();
          }, 100);
        });
      }

      // 如果是移除事件，OCR 数据保留在全局存储中
      if (evt.removed && evt.removed.element) {
        const file = evt.removed.element;
      }
    },

    /**
     * 刷新所有容器的 OCR 数据
     */
    refreshAllContainersOcrData() {

      const components = [
        this.$refs.mainImageUpload,
        this.$refs.packageImageUpload,
        this.$refs.instructionImageUpload
      ].filter(comp => comp);

      components.forEach(comp => {
        if (comp && typeof comp.refreshAllOcrData === 'function') {
          comp.refreshAllOcrData();
        }
      });
    },

    /**
     * 启动拖拽滚动功能
     */
    startDragScroll(event) {
      if (this.dragScrollManager.isActive) return;

      this.dragScrollManager.isActive = true;
      this.dragScrollManager.scrollContainer = this.getScrollContainer();
      this.dragScrollManager.lastMouseY = event.clientY;

      // 确保触发区域已根据当前屏幕尺寸更新
      this.updateTriggerZoneForScreenSize();

      // 添加视觉反馈类
      if (this.$el) {
        this.$el.classList.add('drag-scroll-active');
      }

      // 预检查当前鼠标位置，如果已在触发区域内，立即开始滚动检测
      this.checkAndScroll(event);

      // 添加全局鼠标移动监听
      document.addEventListener('mousemove', this.handleDragScrollMove);
      document.addEventListener('mouseup', this.stopDragScroll);
      document.addEventListener('dragend', this.stopDragScroll); // 额外的安全措施

      // 添加额外的事件监听以提高响应性
      document.addEventListener('dragover', this.handleDragScrollMove);
    },

    /**
     * 处理拖拽过程中的鼠标移动
     */
    handleDragScrollMove(event) {
      if (!this.dragScrollManager.isActive) {
        console.log('[DragScroll] 拖拽滚动未激活');
        return;
      }

      // 节流处理，避免过度频繁的滚动
      if (this.dragScrollManager.throttleTimer) {
        console.log('[DragScroll] 节流中，跳过此次移动事件');
        return;
      }

      const throttleDelay = this.dragScrollManager.config.throttleDelay;
      // console.log('[DragScroll] 设置节流定时器，延迟:', throttleDelay);

      this.dragScrollManager.throttleTimer = setTimeout(() => {
        this.dragScrollManager.throttleTimer = null;
        // console.log('[DragScroll] 节流定时器触发，执行滚动检查');
        this.checkAndScroll(event);
      }, throttleDelay);
    },

    /**
     * 检查鼠标位置并执行滚动
     */
    checkAndScroll(event) {
      if (!this.dragScrollManager.isActive || !this.dragScrollManager.scrollContainer) {
        console.log('[DragScroll] 检查失败: isActive=', this.dragScrollManager.isActive, 'scrollContainer=', !!this.dragScrollManager.scrollContainer);
        return;
      }

      const mouseY = event.clientY;
      const windowHeight = window.innerHeight;
      const triggerZone = this.dragScrollManager.triggerZone;

      let scrollDirection = 0;
      let scrollSpeed = this.dragScrollManager.scrollSpeed;
      const config = this.dragScrollManager.config;

      // console.log('[DragScroll] 检查滚动位置:', {
      //   mouseY,
      //   windowHeight,
      //   triggerZone,
      //   topTrigger: mouseY < triggerZone,
      //   bottomTrigger: mouseY > windowHeight - triggerZone
      // });

      // 检查是否在顶部触发区域
      if (mouseY < triggerZone) {
        scrollDirection = -1;
        // 计算距离边缘的比例（0-1，越接近边缘值越大）
        const distanceFromEdge = triggerZone - mouseY;
        const intensity = Math.max(0.1, distanceFromEdge / triggerZone); // 降低最小强度从0.2到0.1

        // 应用加速度配置
        let finalIntensity = intensity;
        if (config.acceleration.enabled && intensity > config.acceleration.threshold) {
          finalIntensity = intensity * config.acceleration.factor;
        }

        scrollSpeed = Math.max(config.minScrollSpeed, Math.min(config.maxScrollSpeed, scrollSpeed * finalIntensity));

        // console.log('[DragScroll] 向上滚动触发:', {
        //   distanceFromEdge,
        //   intensity,
        //   finalIntensity,
        //   scrollSpeed,
        //   scrollDirection
        // });

        // 添加顶部滚动区域视觉反馈
        if (config.enableVisualFeedback && this.$el) {
          this.$el.classList.add('scroll-zone-top');
          this.$el.classList.remove('scroll-zone-bottom');
          // 根据强度调整视觉反馈的透明度
          this.$el.style.setProperty('--scroll-zone-opacity', Math.min(1, intensity * 2));
        }
      }
      // 检查是否在底部触发区域
      else if (mouseY > windowHeight - triggerZone) {
        scrollDirection = 1;
        // 计算距离边缘的比例
        const distanceFromEdge = mouseY - (windowHeight - triggerZone);
        const intensity = Math.max(0.1, distanceFromEdge / triggerZone); // 降低最小强度

        // 应用加速度配置
        let finalIntensity = intensity;
        if (config.acceleration.enabled && intensity > config.acceleration.threshold) {
          finalIntensity = intensity * config.acceleration.factor;
        }

        scrollSpeed = Math.max(config.minScrollSpeed, Math.min(config.maxScrollSpeed, scrollSpeed * finalIntensity));

        // 添加底部滚动区域视觉反馈
        if (config.enableVisualFeedback && this.$el) {
          this.$el.classList.add('scroll-zone-bottom');
          this.$el.classList.remove('scroll-zone-top');
          // 根据强度调整视觉反馈的透明度
          this.$el.style.setProperty('--scroll-zone-opacity', Math.min(1, intensity * 2));
        }
      } else {
        // 移除滚动区域视觉反馈
        if (config.enableVisualFeedback && this.$el) {
          this.$el.classList.remove('scroll-zone-top', 'scroll-zone-bottom');
          this.$el.style.removeProperty('--scroll-zone-opacity');
        }
      }

      // 执行滚动
      if (scrollDirection !== 0) {
        this.performScroll(scrollDirection, scrollSpeed);
      }
    },

    /**
     * 执行滚动操作
     */
    performScroll(direction, speed, forceScroll = false) {
      if (!this.dragScrollManager.scrollContainer) {
        console.warn('[DragScroll] 滚动容器不存在');
        return;
      }

      const scrollAmount = direction * speed;
      const config = this.dragScrollManager.config;

      // 检查滚动边界，避免过度滚动
      const container = this.dragScrollManager.scrollContainer;
      const currentScrollTop = container.scrollTop;
      const maxScrollTop = container.scrollHeight - container.clientHeight;

      // console.log('[DragScroll] 执行滚动:', {
      //   direction,
      //   speed,
      //   scrollAmount,
      //   currentScrollTop,
      //   maxScrollTop,
      //   containerHeight: container.clientHeight,
      //   scrollHeight: container.scrollHeight,
      //   canScrollUp: currentScrollTop > 0,
      //   canScrollDown: currentScrollTop < maxScrollTop,
      //   forceScroll
      // });

      // 如果不是强制滚动，进行边界检查
      if (!forceScroll) {
        // 向上滚动时检查是否已到顶部 - 放宽边界条件
        if (direction < 0 && currentScrollTop <= 2) { // 进一步放宽到 <= 2，确保有足够的滚动空间
          console.log('[DragScroll] 已到达顶部，停止向上滚动', { currentScrollTop });
          return;
        }

        // 向下滚动时检查是否已到底部
        if (direction > 0 && currentScrollTop >= maxScrollTop - 2) { // 同样放宽底部边界
          console.log('[DragScroll] 已到达底部，停止向下滚动', { currentScrollTop, maxScrollTop });
          return;
        }
      }

      // 执行滚动
      const beforeScrollTop = container.scrollTop;

      if (config.smoothScrolling) {
        container.scrollBy({
          top: scrollAmount,
          behavior: 'smooth'
        });
      } else {
        // 使用即时滚动，性能更好
        container.scrollTop += scrollAmount;
      }

      const afterScrollTop = container.scrollTop;
      // console.log('[DragScroll] 滚动执行结果:', {
      //   beforeScrollTop,
      //   afterScrollTop,
      //   actualScrolled: afterScrollTop - beforeScrollTop,
      //   expectedScrollAmount: scrollAmount
      // });
    },

    /**
     * 停止拖拽滚动功能
     */
    stopDragScroll() {
      if (!this.dragScrollManager.isActive) return;

      this.dragScrollManager.isActive = false;

      // 清理定时器
      if (this.dragScrollManager.scrollTimer) {
        clearInterval(this.dragScrollManager.scrollTimer);
        this.dragScrollManager.scrollTimer = null;
      }

      if (this.dragScrollManager.throttleTimer) {
        clearTimeout(this.dragScrollManager.throttleTimer);
        this.dragScrollManager.throttleTimer = null;
      }

      // 移除视觉反馈类和CSS变量
      if (this.$el) {
        this.$el.classList.remove('drag-scroll-active', 'scroll-zone-top', 'scroll-zone-bottom');
        this.$el.style.removeProperty('--scroll-zone-opacity');
      }

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.handleDragScrollMove);
      document.removeEventListener('mouseup', this.stopDragScroll);
      document.removeEventListener('dragend', this.stopDragScroll);
      document.removeEventListener('dragover', this.handleDragScrollMove);
    },

    /**
     * 获取滚动容器
     */
    getScrollContainer() {
      // console.log('[DragScroll] 开始查找滚动容器...');

      // 优先使用最近的可滚动父元素
      let element = this.$el;
      let depth = 0;

      while (element && element !== document.body && depth < 10) { // 限制查找深度
        const style = window.getComputedStyle(element);
        const isScrollable = style.overflowY === 'auto' || style.overflowY === 'scroll' ||
                           style.overflow === 'auto' || style.overflow === 'scroll';

        // console.log(`[DragScroll] 检查元素 ${depth}:`, {
        //   tagName: element.tagName,
        //   className: element.className,
        //   overflowY: style.overflowY,
        //   overflow: style.overflow,
        //   isScrollable,
        //   scrollHeight: element.scrollHeight,
        //   clientHeight: element.clientHeight,
        //   canScroll: element.scrollHeight > element.clientHeight
        // });

        if (isScrollable && element.scrollHeight > element.clientHeight) {
          // console.log('[DragScroll] 找到可滚动容器:', element);
          return element;
        }

        element = element.parentElement;
        depth++;
      }

      // 检查 documentElement
      const docElement = document.documentElement;
      // console.log('[DragScroll] 检查 documentElement:', {
      //   scrollHeight: docElement.scrollHeight,
      //   clientHeight: docElement.clientHeight,
      //   canScroll: docElement.scrollHeight > docElement.clientHeight
      // });

      // 回退到 window 滚动
      const container = document.documentElement || document.body;
      // console.log('[DragScroll] 使用默认容器:', container.tagName);
      return container;
    },

    /**
     * 为子组件提供拖拽滚动接口
     */
    getDragScrollManager() {
      return {
        start: this.startDragScroll,
        stop: this.stopDragScroll,
        isActive: () => this.dragScrollManager.isActive,
        test: this.testScrollFunctionality // 添加测试方法
      };
    },

    /**
     * 测试滚动功能（开发调试用）
     */
    testScrollFunctionality() {
      console.log('[DragScroll] 开始测试滚动功能...');

      const container = this.getScrollContainer();
      console.log('[DragScroll] 测试容器:', {
        tagName: container.tagName,
        className: container.className,
        scrollTop: container.scrollTop,
        scrollHeight: container.scrollHeight,
        clientHeight: container.clientHeight,
        canScrollUp: container.scrollTop > 0,
        canScrollDown: container.scrollTop < container.scrollHeight - container.clientHeight
      });

      // 测试向上滚动（强制滚动，绕过边界检查）
      console.log('[DragScroll] 测试向上滚动...');
      this.performScroll(-1, 50, true);

      setTimeout(() => {
        console.log('[DragScroll] 向上滚动后的位置:', container.scrollTop);

        // 测试向下滚动
        console.log('[DragScroll] 测试向下滚动...');
        this.performScroll(1, 50, true);

        setTimeout(() => {
          console.log('[DragScroll] 向下滚动后的位置:', container.scrollTop);
        }, 100);
      }, 100);
    },

    /**
     * 初始化拖拽滚动功能
     */
    initializeDragScroll() {
      // 设置滚动容器
      this.dragScrollManager.scrollContainer = this.getScrollContainer();

      // 根据屏幕尺寸调整触发区域
      this.updateTriggerZoneForScreenSize();

      // 监听窗口大小变化，动态调整触发区域
      window.addEventListener('resize', this.updateTriggerZoneForScreenSize);

      // 监听全局拖拽事件（通过 sortable 的全局事件）
      document.addEventListener('dragstart', this.handleGlobalDragStart);
      document.addEventListener('dragend', this.handleGlobalDragEnd);
    },

    /**
     * 清理拖拽滚动功能
     */
    cleanupDragScroll() {
      // 停止当前的拖拽滚动
      this.stopDragScroll();

      // 移除全局事件监听
      window.removeEventListener('resize', this.updateTriggerZoneForScreenSize);
      document.removeEventListener('dragstart', this.handleGlobalDragStart);
      document.removeEventListener('dragend', this.handleGlobalDragEnd);
    },

    /**
     * 根据屏幕尺寸更新触发区域
     */
    updateTriggerZoneForScreenSize() {
      const screenWidth = window.innerWidth;
      const zones = this.dragScrollManager.config.responsiveTriggerZones;

      if (screenWidth <= 1366) {
        this.dragScrollManager.triggerZone = zones.small;
      } else if (screenWidth <= 1919) {
        this.dragScrollManager.triggerZone = zones.medium;
      } else {
        this.dragScrollManager.triggerZone = zones.large;
      }

      // 同时更新视觉反馈的CSS变量
      if (this.$el) {
        this.$el.style.setProperty('--drag-scroll-trigger-zone', `${this.dragScrollManager.triggerZone}px`);
      }
    },

    /**
     * 处理全局拖拽开始事件
     */
    handleGlobalDragStart(event) {
      // 检查是否是图片拖拽事件
      if (this.isDragFromImageContainer(event.target)) {
        this.startDragScroll(event);
      }
    },

    /**
     * 处理全局拖拽结束事件
     */
    handleGlobalDragEnd() {
      // 无论如何都停止拖拽滚动
      this.stopDragScroll();
    },

    /**
     * 检查拖拽是否来自图片容器
     */
    isDragFromImageContainer(element) {
      // 向上查找，检查是否在图片容器内
      let current = element;
      while (current && current !== document.body) {
        if (current.classList && (
          current.classList.contains('draggable-image-upload-container') ||
          current.classList.contains('image-item-container') ||
          current.classList.contains('draggable-item')
        )) {
          return true;
        }
        current = current.parentElement;
      }
      return false;
    },

    /**
     * 获取拖拽滚动状态信息（用于调试）
     */
    getDragScrollStatus() {
      return {
        isActive: this.dragScrollManager.isActive,
        triggerZone: this.dragScrollManager.triggerZone,
        scrollSpeed: this.dragScrollManager.scrollSpeed,
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
        config: this.dragScrollManager.config
      };
    },

    /**
     * 手动测试拖拽滚动功能（用于调试）
     */
    testDragScroll(mouseY) {
      if (!this.dragScrollManager.isActive) {
        console.warn('[DragScroll] 拖拽滚动未激活，请先开始拖拽操作');
        return;
      }

      const mockEvent = { clientY: mouseY || window.innerHeight / 2 };
      this.checkAndScroll(mockEvent);

      // console.log('[DragScroll] 测试滚动检查完成', {
      //   mouseY: mockEvent.clientY,
      //   triggerZone: this.dragScrollManager.triggerZone,
      //   windowHeight: window.innerHeight
      // });
    },
  },
};
</script>
<style lang="scss" scoped>
.image-review-container {
  padding: 20px;

  .image-audit-section {
    margin-bottom: 30px;

    .audit-header {
      display: flex;
      align-items: center;
      padding: 15px 0;
      cursor: pointer;
      border-bottom: 1px solid #e4e7ed;

      .audit-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-right: 8px;
      }

      .audit-help-icon {
        color: #909399;
        margin-right: 8px;
        cursor: help;

        &:hover {
          color: #409eff;
        }
      }

      .audit-toggle-icon {
        color: #909399;
        margin-left: auto;
        transition: transform 0.3s ease;

        &:hover {
          color: #409eff;
        }
      }
    }

    .audit-content {
      padding-top: 20px;

      .image-section {
        margin-bottom: 30px;

        .image-title {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 15px;

          .image-limit {
            color: #bababa;
            font-weight: normal;
            font-size: 12px;
          }
        }

        .image-content {
          min-height: 120px;

          .main-drag-area,
          .package-drag-area,
          .instruction-drag-area {
            min-height: 120px;
            border: 2px dashed transparent;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #c0c4cc;
            }
          }

          .main-drag-area {
            &.sortable-ghost {
              border-color: #409eff;
              background-color: rgba(64, 158, 255, 0.1);
            }
          }

          .package-drag-area,
          .instruction-drag-area {
            &.sortable-ghost {
              border-color: #67c23a;
              background-color: rgba(103, 194, 58, 0.1);
            }
          }
        }
      }
    }
  }

  .channel-image-controls {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    /deep/ .el-form-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    /deep/ .el-form-item__label {
      font-weight: 600;
      color: #303133;
    }

    /deep/ .el-radio-group {
      .el-radio {
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

// 与 SKU 组件保持一致的样式
.image-box2 {
  display: flex;
  margin-bottom: 30px;

  .img-title {
    width: 120px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
    margin-right: 20px;
    flex-shrink: 0;
  }

  .img-content {
    flex: 1;
  }
}

/* 拖拽滚动相关样式 */
.image-review-container {
  /* 确保容器可以滚动 */
  overflow-y: auto;

  /* CSS变量定义 */
  --drag-scroll-trigger-zone: 150px;
  --scroll-zone-opacity: 0.6;

  /* 拖拽时的滚动指示器 */
  &.drag-scroll-active {
    position: relative;

    &::before {
      content: '↑ 拖拽到此处向上滚动';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: var(--drag-scroll-trigger-zone);
      background: linear-gradient(to bottom,
        rgba(64, 158, 255, calc(var(--scroll-zone-opacity, 0.6) * 0.2)),
        rgba(64, 158, 255, calc(var(--scroll-zone-opacity, 0.6) * 0.05)),
        transparent);
      pointer-events: none;
      z-index: 1000;
      opacity: 0;
      transition: opacity 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #409eff;
      font-size: 14px;
      font-weight: 500;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    &::after {
      content: '↓ 拖拽到此处向下滚动';
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      height: var(--drag-scroll-trigger-zone);
      background: linear-gradient(to top,
        rgba(64, 158, 255, calc(var(--scroll-zone-opacity, 0.6) * 0.2)),
        rgba(64, 158, 255, calc(var(--scroll-zone-opacity, 0.6) * 0.05)),
        transparent);
      pointer-events: none;
      z-index: 1000;
      opacity: 0;
      transition: opacity 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #409eff;
      font-size: 14px;
      font-weight: 500;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    &.scroll-zone-top::before {
      opacity: var(--scroll-zone-opacity, 0.6);
      background: linear-gradient(to bottom,
        rgba(103, 194, 58, calc(var(--scroll-zone-opacity, 0.6) * 0.25)),
        rgba(103, 194, 58, calc(var(--scroll-zone-opacity, 0.6) * 0.08)),
        transparent);
      color: #67c23a;
      animation: pulse-top 1.5s infinite;
    }

    &.scroll-zone-bottom::after {
      opacity: var(--scroll-zone-opacity, 0.6);
      background: linear-gradient(to top,
        rgba(103, 194, 58, calc(var(--scroll-zone-opacity, 0.6) * 0.25)),
        rgba(103, 194, 58, calc(var(--scroll-zone-opacity, 0.6) * 0.08)),
        transparent);
      color: #67c23a;
      animation: pulse-bottom 1.5s infinite;
    }
  }
}

/* 拖拽时的平滑滚动 */
.image-review-container.drag-scroll-active {
  scroll-behavior: auto; /* 拖拽时使用即时滚动 */
}

/* 拖拽区域的视觉增强 */
.image-section {
  transition: all 0.3s ease;

  /* 拖拽时突出显示可放置区域 */
  &.drag-target-highlight {
    background-color: rgba(64, 158, 255, 0.05);
    border: 2px dashed #409eff;
    border-radius: 8px;
  }
}

/* 拖拽占位符的增强样式 */
.drag-placeholder {
  transition: all 0.3s ease;

  &.drag-over {
    border-color: #67c23a !important;
    background-color: rgba(103, 194, 58, 0.1) !important;
    transform: scale(1.02);
  }
}

/* 脉冲动画 */
@keyframes pulse-top {
  0%, 100% {
    transform: translateY(0);
    opacity: var(--scroll-zone-opacity, 0.6);
  }
  50% {
    transform: translateY(5px);
    opacity: calc(var(--scroll-zone-opacity, 0.6) * 1.2);
  }
}

@keyframes pulse-bottom {
  0%, 100% {
    transform: translateY(0);
    opacity: var(--scroll-zone-opacity, 0.6);
  }
  50% {
    transform: translateY(-5px);
    opacity: calc(var(--scroll-zone-opacity, 0.6) * 1.2);
  }
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 1366px) {
  .image-review-container {
    --drag-scroll-trigger-zone: 120px;

    &.drag-scroll-active {
      &::before,
      &::after {
        font-size: 12px;
      }
    }
  }
}

/* 中等屏幕优化 */
@media (min-width: 1367px) and (max-width: 1919px) {
  .image-review-container {
    --drag-scroll-trigger-zone: 150px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1920px) {
  .image-review-container {
    --drag-scroll-trigger-zone: 180px;

    &.drag-scroll-active {
      &::before,
      &::after {
        font-size: 16px;
      }
    }
  }
}

/* 移动设备优化 */
@media (max-width: 768px) {
  .image-review-container {
    --drag-scroll-trigger-zone: 100px;

    &.drag-scroll-active {
      &::before,
      &::after {
        font-size: 11px;
        padding: 0 10px;
        text-align: center;
      }
    }
  }
}
</style>