<template>
  <div class="image-content">
    <img class="el-upload-list__item-thumbnail" v-if="file.status == 'success'" :src="getImageSrc(file)" alt
      width="100%" height="100%" @error="handleImageError" />
    <el-progress v-if="file.status === 'uploading' || file.status === 'ready'" :type="'circle'" :stroke-width="6"
      :percentage="parsePercentage(file.percentage)">
    </el-progress>
    <span class="el-upload-list__item-actions" v-if="file.status !== 'uploading'">
      <span @click="$emit('left', file)" v-if="!disabled" title="左移">
        <i class="el-icon-back"></i>
      </span>

      <span @click="$emit('preview', file)" v-if="preview" title="预览">
        <i class="el-icon-zoom-in"></i>
      </span>

      <span @click="$emit('remove', file)" v-if="!disabled" title="删除">
        <i class="el-icon-delete"></i>
      </span>

      <span @click="$emit('right', file)" v-if="!disabled" title="右移">
        <i class="el-icon-right"></i>
      </span>
    </span>

    <!-- OCR按钮紧贴图片底部 -->
    <div class="ocr-button-container" v-if="enableOCR">
      <!-- 加载中状态 -->
      <span v-if="ocrResult.loading" class="ocr-extract-btn loading">
        提取中
      </span>
      <!-- 可点击状态：图片已上传成功或已有图片URL -->
      <span v-else-if="isImageReady" class="ocr-extract-btn clickable" @click="$emit('extractOCR', file)">
        提取图片文字
      </span>
      <!-- 不可用状态 -->
      <span v-else class="ocr-extract-btn disabled">
        等待上传完成
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ImageItemContent",
  props: {
    file: {
      type: Object,
      required: true,
    },
    enableOCR: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    preview: {
      type: Boolean,
      default: false,
    },
    ocrResult: {
      type: Object,
      default: () => ({
        loading: false,
        extracted: false,
        fields: []
      }),
    },
    isImageReady: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    parsePercentage(val) {
      return parseInt(val, 10);
    },

    handleImageError(event) {
      console.error('图片加载失败:', {
        file: this.file,
        src: event.target.src,
        error: event
      });

      // 尝试使用备用 URL
      const currentSrc = event.target.src;
      let fallbackSrc = null;

      // 如果当前使用的是 blob URL，尝试使用 mediaUrl
      if (currentSrc.startsWith('blob:') && this.file.mediaUrl && !this.file.mediaUrl.startsWith('blob:')) {
        fallbackSrc = this.file.mediaUrl;
      }
      // 如果当前使用的是 mediaUrl，尝试使用 response.data.mediaUrl
      else if (this.file.response && this.file.response.data && this.file.response.data.mediaUrl && currentSrc !== this.file.response.data.mediaUrl) {
        fallbackSrc = this.file.response.data.mediaUrl;
      }

      if (fallbackSrc) {
        event.target.src = fallbackSrc;
      } else {
        console.error('ImageItemContent: No fallback URL available for file:', this.file);
      }
    },

    // 获取图片显示的 src，优先使用正确的 URL
    getImageSrc(file) {

      // 第一优先级：使用接口返回的 mediaUrl
      if (file.mediaUrl && !file.mediaUrl.startsWith('blob:')) {
        return file.mediaUrl;
      }

      // 第二优先级：从 response.data 中获取 mediaUrl
      if (file.response && file.response.data && file.response.data.mediaUrl) {
        return file.response.data.mediaUrl;
      }

      // 第三优先级：使用 file.url（但排除 blob URL）
      if (file.url && !file.url.startsWith('blob:')) {
        return file.url;
      }

      // 最后备用：如果没有其他选择，使用 blob URL（但会记录警告）
      if (file.url && file.url.startsWith('blob:')) {
        console.warn('ImageItemContent: Falling back to blob URL (may not work):', file.url);
        return file.url;
      }

      // 如果都没有，使用 mediaUrl（即使是 blob）
      if (file.mediaUrl) {
        console.warn('ImageItemContent: Using mediaUrl as last resort:', file.mediaUrl);
        return file.mediaUrl;
      }

      console.error('ImageItemContent: No valid image src found for file:', file);
      return '';
    },

    // 获取图片显示的 src
    getImageSrc(file) {

      // 优先使用 mediaUrl（上传后的正确URL）
      if (file.mediaUrl) {
        return file.mediaUrl;
      }

      // 其次使用 url
      if (file.url) {
        return file.url;
      }

      // 尝试从 response 中获取
      if (file.response && file.response.data) {
        if (file.response.data.mediaUrl) {
          return file.response.data.mediaUrl;
        }
        if (file.response.data.url) {
          return file.response.data.url;
        }
      }

      console.warn('ImageItemContent: No valid image src found for file:', file);
      return '';
    },
  },
};
</script>

<style lang="scss" scoped>
.image-content {
  position: relative;
  width: 100%;
  height: 100%;

  .el-upload-list__item-thumbnail {
    object-fit: cover;
  }

  .el-upload-list__item-actions {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    &:hover {
      opacity: 1;
    }

    span {
      cursor: pointer;
      padding: 5px;
      border-radius: 3px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .ocr-button-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding: 5px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);

    .ocr-extract-btn {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: white;
      text-align: center;
      min-width: 80px;

      &.loading {
        background-color: #909399;
        cursor: not-allowed;
      }

      &.clickable {
        background-color: #409eff;
        cursor: pointer;

        &:hover {
          background-color: #66b1ff;
        }
      }

      &.disabled {
        background-color: #c0c4cc;
        cursor: not-allowed;
      }
    }
  }
}
</style>
