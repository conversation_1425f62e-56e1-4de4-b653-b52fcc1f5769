<template>
  <div class="component-container">
    <div class="loading" v-loading="productLoading" v-show="productLoading"></div>
    <el-tabs type="border-card">
      <el-tab-pane label="基础属性">
        <approval-process :approvalData="approvalData"></approval-process>
        <spu ref="spu" @isShowBtn="isShowBtn" :spuData="spuData" :skuData="skuData" :coorection="coorection"></spu>
        <sku ref="sku" :skuData="skuData" :sauData="sauData" @editSku="editSku" :coorection="coorection" :disableStatus="disableStatus"></sku>
        <label-attr
          ref="labelAttr"
          :skuData="skuData"
          :spuData="spuData"
          :loadForm="false"
          :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"
        ></label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE' && urlParam.from != 'spuOperate'"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE' && urlParam.from != 'spuOperate'"></extended-attr2>
        <extended-attr3 ref="extend"el-tabs v-if="spuCategory.type == 'MEDICAL_INSTRUMENT' && urlParam.from != 'spuOperate'"></extended-attr3>
      </el-tab-pane>
      <el-tab-pane label="资质属性">
        <qualification-attr></qualification-attr>
      </el-tab-pane>
      <el-tab-pane label="精修图版本">
        <sales-attr :imgTitle="imgTitle"></sales-attr>
      </el-tab-pane>
    </el-tabs>

    <el-row class="bottom-btns">
      <!-- 常规编辑 -->
      <div :span="24" class="bottom-btn-wrap" v-if="!detailState">
        <el-button :disabled="banSubmmit" type="primary" @click="submit()">提交</el-button>
        <el-button
          type="primary"
          v-show="
            $store.getters.spuCategory.type !== 'GENERAL_MEDICINE' && $store.getters.spuCategory.type !== 'MEDICAL_INSTRUMENT' && !hiddenBtn
          "
          @click="submit('checkSpu')"
          >spu唯一性校验</el-button
        >
      </div>
      <!-- 先为详情，再变更为编辑时 -->
      <div :span="24" class="text-rt bottom-btn-wrap" v-if="detailState">
        <!-- 纠错驳回 -->
        <el-button type="primary" @click="showRejectAlert()" v-show="urlParam.type == 'coorection' && !isEdit">驳回</el-button>
        <el-button type="primary" @click="changeType('edit')" v-show="urlParam.type == 'coorection' && !isEdit">编辑修改</el-button>
        <!-- 新品上架 -->
        <el-button type="primary" @click="allow()" v-show="urlParam.type == 'shelves' && !isEdit">允许上架</el-button>
        <el-button type="primary" @click="changeType('auditLevel2')" v-show="urlParam.type == 'shelves' && !isEdit">编辑修改</el-button>

        <el-button :disabled="banSubmmit" type="primary" @click="submit()" v-show="isEdit">提交</el-button>
        <el-button
          type="primary"
          v-show="
            $store.getters.spuCategory.type !== 'GENERAL_MEDICINE' && $store.getters.spuCategory.type !== 'MEDICAL_INSTRUMENT' && !hiddenBtn
          "
          @click="submit('checkSpu')"
          >spu唯一性校验{{ $store.getters.spuCategory.type }}</el-button
        >
      </div>
    </el-row>

    <el-dialog title="请输入驳回原因（必填）" :visible.sync="rejectReasonDlg" width="30%">
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="条码和商品资料不符"></el-checkbox>
        <el-checkbox label="条码无法核实"></el-checkbox>
        <el-checkbox label="图片和资料不符"></el-checkbox>
        <el-checkbox label="图片信息不全"></el-checkbox>
        <el-checkbox label="图片不清晰"></el-checkbox>
        <el-checkbox label="中台已存在"></el-checkbox>
        <el-checkbox label="纠错信息错误"></el-checkbox>
      </el-checkbox-group>
      <el-input v-model="reason" type="textarea" :rows="2" maxlength="80" placeholder="自定义驳回原因" clearable show-word-limit></el-input>
      <span slot="footer">
        <el-button size="medium" @click="rejectReasonDlg = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="reject()">确 定</el-button>
      </span>
    </el-dialog>
    <goodsRejectAlert ref="goodsRejectAlert" @sure="goodsRejectAlertSureFc"></goodsRejectAlert>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import goodsRejectAlert from "./goodsRejectAlert.vue"
import { productMixinBase } from "@/mixin/productMixinBase.js"
import {
  getProductData, //获取商品信息
  spuModify, //spu修改
  productModify, //商品修改
  productModifyOperate, // 预首营商品修改
  getApplyInfo, //获取审批流信息
  checkSpuSingleApi, //检查SPU唯一性
  correctionReject, // 商品纠错驳回
  allowShelves, //允许上架
  getPeopleRole, //获取预首营弹框权限
  formDisableStatus, //sku和sau编辑权限
} from "@/api/product"
import qualificationAttr from "./qualificationAttr"
import salesAttr from "./salesAttr"
export default {
  name: "",
  mixins: [productMixinBase],
  components: { qualificationAttr, salesAttr, goodsRejectAlert },
  watch: {},
  data() {
    return {
      banSubmmit: false, //防抖节流标识
      disableStatus: false,
      showYuDialog: 0, //是否显示预首营弹框
      hiddenBtn: false,
      imgTitle: "",
      coorection: {},
      detailState: false, // 是否显示为详情状态
      isEdit: false, // 是否为纠错编辑状态
      rejectReasonDlg: false,
      checkList: [],
      reason: "",
    }
  },
  created() {
    this.formDisableStatus()
    this.getRole()
    this.init()
  },
  computed: {
    spuCategory() {
      return this.$store.state.product.spuCategory
    },
  },
  mounted() {},
  methods: {
    init() {
      if (this.urlParam.from && this.urlParam.from == "operate") {
        // 设置商品操作类型为预首营
        this.$store.commit("product/SET_OPERATION_TYPE", "operate")
        // 纠错列表进入时，设置初始状态未为详情
      } else if (this.urlParam.from && this.urlParam.from == "spuOperate") {
        // 设置商品操作类型为spu修改
        this.$store.commit("product/SET_OPERATION_TYPE", "spuOperate")
        // 纠错列表进入时，设置初始状态未为详情
      } else if (this.urlParam.type && this.urlParam.type == "coorection") {
        this.coorectionData = JSON.parse(localStorage.getItem("coorectionData"))
        let coorectionObj = {}
        for (let key of this.coorectionData.detailDtoList) {
          coorectionObj[key.correctKey] = key.correctValue
          if (key.correctKey === "skuAliasText") {
            coorectionObj["alias"] = key.correctValue
          }
        }
        this.coorection = coorectionObj
        this.detailState = true
        this.$store.commit("product/SET_OPERATION_TYPE", "detail")
      } else if (this.urlParam.type && this.urlParam.type == "shelves") {
        this.coorectionData = JSON.parse(localStorage.getItem("coorectionData"))
        this.detailState = true
        this.$store.commit("product/SET_OPERATION_TYPE", "detail")
      } else {
        // 设置商品操作类型为修改
        this.$store.commit("product/SET_OPERATION_TYPE", "edit")
      }
      // 获取审批流信息
      this.getApplyInfo({
        selectType: 2, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: this.urlParam.sendCode, //单据编号
        productCode: this.urlParam.productCode, //商品编码
        productType: this.urlParam.productType, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: this.urlParam.spuCode, //SPU编码
      })
      this.getProductInfo(this.urlParam.spuCode)
    },
    showRejectAlert() {
      this.$refs.goodsRejectAlert.open()
      // this.rejectReasonDlg = true;
    },
    async goodsRejectAlertSureFc(data) {
      let params = Object.assign({}, data, { uniqueCode: this.urlParam.uniqueCode })

      try {
        this.productLoading = true
        let res = await correctionReject(params)
        this.productLoading = false
        if (res.success) {
          try {
            if(type !== 'coorection'){
              parent.CreateTab("../static/dist/index.html#/list/correction", "商品纠错记录", true)
            }
            parent.CloseTab("../static/dist/index.html#/product/editProduct") // 关闭当前页面
          } catch (error) {
            this.$router.push({
              name: "correction",
            })
            console.error(error)
          }
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        console.error(error)
      }
    },
    // 获取预首营弹框权限
    async getRole() {
      try {
        const { data } = await getPeopleRole()
        this.showYuDialog = data
      } catch (error) {}
    },
    /**
     * @description: 商品商品数据
     * @param {string}  spuCode
     * @return:
     */
    async getProductInfo(spuCode) {
      try {
        this.productLoading = true
        let res = await getProductData({
          productCode: spuCode,
          productType: 1,
        })
        this.spuData = res.data.spu
        this.skuData = Object.freeze(this.skuSort(res.data.sku))
        this.sauData = Object.freeze(res.data.sau)
        if (!this.skuData[0]) return
        this.imgTitle = `${this.skuData[0].skuCode}-${this.skuData[0].productId}-${this.skuData[0].smallPackageCode}-${this.skuData[0].skuName}`
      } catch (error) {
        console.error('获取商品数据失败:', error)
        this.$message.error('获取商品数据失败，请重试')
        this.productLoading = false // 确保在错误情况下也重置状态
      }
    },
    /**
     * @description: sku排序处理，将可修改的放到第一个
     * @param {array}  skuList 处理的SKU数据
     * @return:
     */
    skuSort(skuList) {
      // 如果是SKU修改
      if (this.urlParam.productType == 2) {
        let index = skuList.findIndex((item) => {
          return item.skuCode == this.urlParam.productCode
        })
        if (index != -1) {
          let first = skuList.splice(index, 1)
          skuList.unshift(first[0])
        }
      }
      return skuList
    },
    /**
     * @description:商品数据提交
     * @param {string} checkSpuSingle 是否为检查SPU唯一触发
     * @return:
     */
    async submit(checkSpuSingle) {
      let spu = await this.$refs.spu.getSpuData()
      let skuData = null,
        sau = null
      if (this.urlParam.from != "spuOperate") {
        skuData = this.$refs.sku.getSkuData()
        // sau = this.$refs.sau.getSauData();
      }
      let extendData = {}
      if (
        (this.spuCategory.type == "GENERAL_MEDICINE" || this.spuCategory.type == "TRADITIONAL_MEDICINE" || this.spuCategory.type == "MEDICAL_INSTRUMENT") &&
        this.urlParam.from != "spuOperate"
      ) {
        extendData = this.$refs.extend.getExtendData()
      }
      if (spu.data.spuCategory == 5) {
        sau = this.sauData
      }
      if (!spu.state || !extendData || (!skuData && this.urlParam.from != "spuOperate")) {
        // 未通过 SPU表单校验
        return
      }
      let spuData = spu.data
      if (checkSpuSingle) {
        this.productLoading = true
        let res = await checkSpuSingleApi(spuData)
        if (res.success) {
          this.$message.success("SPU唯一性检查通过")
        } else {
          this.$message.error(res.retMsg)
        }
        this.productLoading = false
        return false
      }
      // 验证spu唯一性
      if (spuData.spuCategory == 3 || spuData.spuCategory == 4) {
        this.productLoading = true
        let res = await checkSpuSingleApi(spuData)
        this.productLoading = false
        if (!res.success) {
          this.$message.error(res.retMsg)
          return false
        }
      }
      let submitData = {}
      if (this.urlParam.from != "spuOperate") {
        if (!skuData) {
          // 未添加SKU数据
          return
        }
        submitData = this.formatSubmitData(_.cloneDeep(spuData), _.cloneDeep(skuData), _.cloneDeep(sau), _.cloneDeep(extendData))
      } else {
        submitData = this.formatSubmitData(_.cloneDeep(spuData))
      }

      //操作类型 operType,
      // (0:保存草稿,
      // 1:新增spu,
      // 2:属性修改,
      // 3:商品合并,
      // 4:商品停用,
      // 5:三方同步,
      // 6:批量修改,
      // 7:草稿删除,
      // 8:预首营修改
      // 13:商品纠错
      // 12:商品上架)
      submitData.operType = 2
      //商品编码类型(1:spu,2:sku,3:sau)
      submitData.prodType = this.urlParam.productType
      //商品编码
      submitData.prodCode = this.urlParam.productCode
      if (this.operationType == "operate") {
        //预首营修改
        submitData.operType = 8
        submitData.applyCode = this.urlParam.applyCode
        submitData.productCode = this.urlParam.sauCode
        this.submitForOperate(submitData)
        return false
      }
      // 商品纠错修改
      if (this.urlParam.type && this.urlParam.type == "coorection") {
        submitData.operType = 13
        submitData.uniqueCode = this.urlParam.uniqueCode
      }
      // 商品上架
      if (this.urlParam.type == "shelves") {
        submitData.operType = 12
        submitData.uniqueCode = this.urlParam.uniqueCode
      }
      if (skuData) {
        if (skuData[0].smallPackageCodeList && skuData[0].smallPackageCodeList.length > 1) {
          this.$message.error("一个商品最多包含1个小包装条码！")
          return
        } else if (skuData[0].mediumPackageCodeList && skuData[0].mediumPackageCodeList.length > 1) {
          this.$message.error("一个商品最多包含1个中包装条码！")
          return
        } else if (skuData[0].piecePackageCodeList && skuData[0].piecePackageCodeList.length > 1) {
          this.$message.error("一个商品最多包含1个件包装条码！")
          return
        }
      }

      // let hasChange = this.checkSubmitDataHasChange(submitData)
      // if (hasChange === false) {
      //   this.$message.warning("您未变更任何信息")
      //   return
      // }

      if (this.urlParam.preOperateStatus == 1 && this.showYuDialog) {
        submitData.productCode = this.urlParam.sauCode
        this.$confirm("修改商品审核通过后，是否自动提交预首营？", "提示", {
          distinguishCancelAndClose: true,
          confirmButtonText: "自动提交",
          cancelButtonText: "不提交",
          type: "warning",
        })
          .then(() => {
            submitData.doPreOperate = 1
            this.enterSubmit(submitData)
          })
          .catch((action) => {
            if (action === "cancel") {
              submitData.doPreOperate = 0
              this.enterSubmit(submitData)
            }
          })
      } else {
        submitData.productCode = this.urlParam.sauCode
        submitData.doPreOperate = 0
        this.enterSubmit(submitData)
      }
    },

    //检测是否有修改的数据
    checkSubmitDataHasChange(submitData) {
      if (this.checkSkuSubmitDataHasChange(submitData.skuList[0]) === true) return true

      if (submitData.productFieldRecord.spuFieldList && submitData.productFieldRecord.spuFieldList.length > 0) {
        if (this.checkSubmitArrHasChange(submitData.productFieldRecord.spuFieldList, submitData) === true) return true
      }

      if (submitData.productFieldRecord.extendFieldList && submitData.productFieldRecord.extendFieldList.length > 0) {
        if (this.checkSubmitArrHasChange(submitData.productFieldRecord.extendFieldList, submitData.productExtend) === true) return true
      }

      return false
    },

    checkSubmitArrHasChange(submitChangList, submitChangData) {
      for (let index = 0; index < submitChangList.length; index++) {
        const key = submitChangList[index]
        if (submitChangData[key] === this.coorection[key]) {
          return true
        }
      }
      return false
    },

    checkSkuSubmitDataHasChange(sku) {
      let keys = Object.keys(this.coorection)
      for (let index = 0; index < keys.length; index++) {
        const key = keys[index]
        if (sku[key] && sku[key] === this.coorection[key]) {
          return true
        }
      }
      return false
    },

    async enterSubmit(submitData) {
      let res = {}
      this.productLoading = true
      this.banSubmmit = true
      if (this.urlParam.from == "spuOperate") {
        res = await spuModify(submitData)
      } else {
        res = await productModify(submitData)
      }
      this.banSubmmit = false
      this.productLoading = false
      if (res.success) {
        debugger
        this.$message.success("提交成功")
        // 纠错列表
        if (this.urlParam.type == "coorection") {
          // parent.CreateTab("../static/dist/index.html#/list/correction", "商品纠错列表", true)
          // 商品上架申请
        } else if (this.urlParam.type == "shelves") {
          parent.CreateTab("../static/dist/index.html#/list/shelvesList", "商品上架申请", true)
        } else if (this.urlParam.from == "spuOperate") {
          parent.CreateTab("../static/dist/index.html#/product/spuList", "spu列表", true)
        } else {
          parent.CreateTab("../static/dist/index.html#/product/productList", "商品列表", true)
        }
        parent.CloseTab("../static/dist/index.html#/product/editProduct") // 关闭当前页面
      } else {
        this.$message.error(res.retMsg)
      }
    },

    /**
     * 新
     * @description: 纠错时，驳回
     * @param {type}
     * @return:
     */
    async rejectNew() {},
    /**
     * @description: 纠错时，驳回
     * @param {type}
     * @return:
     */
    async reject() {
      if (this.checkList.length === 0 && this.reason === "") {
        this.$message.error("请勾选或自定义驳回原因")
      } else {
        let rejectOpinionNumberList = []
        let temp = [...this.checkList]
        if (this.reason !== "") {
          temp.push(this.reason)
        }
        temp.map((item) => {
          if (item === "条码和商品资料不符") {
            rejectOpinionNumberList.push(1)
          } else if (item === "条码无法核实") {
            rejectOpinionNumberList.push(2)
          } else if (item === "图片和资料不符") {
            rejectOpinionNumberList.push(3)
          } else if (item === "图片信息不全") {
            rejectOpinionNumberList.push(4)
          } else if (item === "图片不清晰") {
            rejectOpinionNumberList.push(5)
          } else if (item === "中台已存在") {
            rejectOpinionNumberList.push(6)
          } else if (item === "纠错信息错误") {
            rejectOpinionNumberList.push(7)
          } else {
            rejectOpinionNumberList.push(8)
          }
        })
        let param = temp.join()
        let rejectOpinionNumber = rejectOpinionNumberList.join()
        try {
          let res = await correctionReject({
            rejectOpinionNumber,
            uniqueCode: this.urlParam.uniqueCode,
            rejectOpinion: param,
          })
          if (res.success) {
            try {
              debugger
              if(this.$route.query.type !== 'coorection'){
                parent.CreateTab("../static/dist/index.html#/list/correction", "商品纠错记录", true)
              }
              parent.CloseTab("../static/dist/index.html#/product/editProduct") // 关闭当前页面
            } catch (error) {
              this.$router.push({
                name: "correction",
              })
              console.error(error)
            }
          } else {
            this.$message.error(res.retMsg)
          }
        } catch (error) {
          console.error(error)
        }
      }
    },
    async allow() {
      try {
        let res = await allowShelves({
          uniqueCode: this.urlParam.uniqueCode,
        })
        if (res.success) {
          parent.CreateTab("../static/dist/index.html#/list/shelvesList", "商品上架申请列表", true)
          parent.CloseTab("../static/dist/index.html#/product/editProduct") // 关闭当前页面
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        console.error(error)
      }
    },
    /**
     * @description:预首营修改
     * @param {object} submitData :提交数据
     * @return:
     */
    async submitForOperate(submitData) {
      this.productLoading = true
      let res = await productModifyOperate(submitData)
      this.productLoading = false
      if (res.success) {
        this.$message.success("提交成功")
        parent.CreateTab("../static/dist/index.html#/product/productList", "商品列表", true)
        parent.CloseTab("../static/dist/index.html#/product/editProduct") // 关闭当前页面
      } else {
        this.$message.error(res.retMsg)
      }
    },
    /**
     * @description: 加载审批信息
     * @param {object}  param {
        selectType: 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode://单据编号
        productCode://商品编码
        productType: //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode://SPU编码
      }
     * @return: 
     */
    async getApplyInfo(param) {
      let res = await getApplyInfo(param)
      if (res.success) {
        this.approvalData = res.data
        // 如果存在纠错记录信息
        if (this.coorectionData && this.urlParam.type == "coorection") {
          this.approvalData.coorection = {
            msg: this.coorectionData.correctionMsg,
            imageList: this.coorectionData.mediaDtoList,
          }
        }
      } else {
        this.$message.error("审批信息请求异常")
      }
    },
    /**
     * @description: 点击编辑修改时相关逻辑处理
     * @param {string} type: SET_OPERATION_TYPE 类型
     * @return:
     */
    changeType(type) {
      // 随便更新一个值触发spu数据的更新
      this.productLoading = true
      // 增加标记，触发子组件spuData watch逻辑更新
      this.$set(this.spuData, "_coorectionChange", true)
      this.$store.commit("product/SET_OPERATION_TYPE", type)
      this.isEdit = true
      if (type === "edit") {
        this.$refs.extend.disabled = false
      }
    },
    // isShowBtn(businessScopeList, spuCategory, id) {
    //   if (spuCategory == 4) {
    //     this.hiddenBtn = true;
    //   } else {
    //     this.hiddenBtn = false;
    //   }
    // },
    isShowBtn(businessScopeList, spuCategory, id) {
      if (spuCategory == 4) {
        let flag = false
        businessScopeList.forEach((item) => {
          item.forEach((list) => {
            if (list == id) {
              flag = true
              return
            }
          })
          if (flag) {
            return
          }
        })
        if (flag) {
          this.hiddenBtn = true
        } else {
          this.hiddenBtn = false
        }
      } else {
        this.hiddenBtn = false
      }
    },
    async formDisableStatus() {
      try {
        const res = await formDisableStatus({ productCode: this.urlParam.productCode })
        if (res.retCode === 0) {
          this.disableStatus = res.data
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        this.$message.error(res.retMsg)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  /deep/ .el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content {
    padding: 0 !important;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
    }
  }
  .bottom-btns {
    // background: #fff;
    width: 100%;
    position: fixed;
    bottom: 0;
    // padding: 15px;
    z-index: 10;
  }
}
.bottom-btn-wrap {
  display: flex;
  justify-content: center;
}
</style>
