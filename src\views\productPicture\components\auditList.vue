<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <div class="loading" v-loading="uploadLoading" v-show="uploadLoading"></div>
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 原图上传时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="领取时间" prop="receiveTime">
              <el-date-picker
                v-model="formData.receiveTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 审核状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核状态">
              <el-select
                v-model="formData.auditStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="未审核" :value="0"></el-option>
                <el-option label="通过" :value="1"></el-option>
                <el-option label="驳回" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 标准库ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="标准库ID">
              <el-input
                v-model="formData.productIds"
                placeholder='请输入标准库ID，多个商品请用","隔开'
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 任务类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务类型">
              <el-select
                v-model="formData.pictureVersionType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value="null"></el-option>
                <el-option label="新建版本" :value="3"></el-option>
                <el-option label="补充原图" :value="2"></el-option>
                <el-option label="绑定版本号" :value="1"></el-option>
                <el-option label="换绑版本号" :value="5"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="百草" :value="1"></el-option>
                <el-option label="系统上传" :value="2"></el-option>
                <el-option label="荷叶健康" :value="3"></el-option>
                <el-option label="智慧脸商城" :value="4"></el-option>
                <el-option label="POP" :value="5"></el-option>
                <el-option label="EC" :value="6"></el-option>
                <el-option label="客服" :value="7"></el-option>
                <el-option label="供多多" :value="8"></el-option>
                <el-option label="智鹿" :value="9"></el-option>
                <el-option label="神农" :value="10"></el-option>
                <el-option label="中台运营" :value="13"></el-option>
                <el-option label="爬虫" :value="20"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人" prop="createUser">
              <el-input
                v-model="formData.createUser"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="bindMechanism">
              <el-select 
                multiple
                collapse-tags
                v-model="formData.bindMechanism" placeholder="请选择"
                @remove-tag="removeTag1"
                @change="changeSelect1"
                clearable
                >

                <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>

                <el-option
                  v-for="item in mechanismOptions"
                  :key="'key_mechanismOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 单据编号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="单据编号" prop="applyCode">
              <el-input
                v-model="formData.applyCode"
                placeholder="请输入单据编号"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否自营 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否自营">
              <el-select
                v-model="formData.businessType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="自营" :value="1"></el-option>
                <el-option label="非自营" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

         <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码" prop="mixtureProduct">
              <el-input
                v-model="formData.mixtureProduct"
                placeholder="输入商品ID，原商品编码，商品编码搜索"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>


          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        :data="tableData"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="sourceName"
          title="来源"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="pictureVersionTypeName"
          title="任务类型"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="auditStatus"
          title="审核状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.auditStatus | filterAuditStatus }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="previewPictureUrl"
          title="审核图片预览"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row, seq }">
            <img @click.stop="auditTask(row, seq)" width="120" height="40" v-if='row.previewPictureUrl' :src="row.previewPictureUrl" alt="">
            <!-- <img @click.stop="auditTask()" width="120" height="40" v-else src="@/assets/images/pass.png" alt=""> -->
          </template></vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="申请时间"
          min-width="150"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <span>{{ row.createTime  | parseTimestamp }}</span>
          </template></vxe-table-column
        >
        <vxe-table-column
          field="preReceiveTime"
          title="领取时间"
          min-width="150"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.preReceiveTime  | parseTimestamp }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="商品信息"
          min-width="150"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.productName }}、{{ row.manufacturerName }}、{{ row.smallPackageCode }}、{{ row.approvalNo }}</span>
          </template>
        </vxe-table-column>
         <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="originalProductCode"
          title="原商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="applyReason"
          title="拍摄原因"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
          <span>{{ ['','新品上架','图片审核驳回','','','图片补全','更新新老包装','新品再上架'][row.applyReason] }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="historyPictureVersionQuantity"
          title="商品版本数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="pictureVersion"
          title="绑定图片版本"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="originalPictureUploadQuantity"
          title="原图上传数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="operatorDeletedQuantity"
          title="运营删除数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="auditOpinion"
          title="审核意见"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row, seq}">
            <span>
              <el-link
                :underline="false"
                :disabled="row.auditStatus != 0"
                type="primary"
                @click.stop="auditTask(row, seq)"
                >{{ row.auditStatus != 0 ? "-" : "审核" }}</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <drag-img
      ref="dragImg"
      @refresh="queryList"
    ></drag-img>
  </div>
</template>

<script>
import {
  getAuditTaskList,
  getMechanismList
} from "@/api/productPicture.js";
import dragImg from "@/components/uploadImg/dragImg"

export default {
  name: "",
  components: { dragImg },
  filters: {
    filterAuditStatus: function (value) {
      switch (value * 1) {
        case 0:
          return "未审核";
        case 1:
          return "通过";
        case 2:
          return "驳回";
        default:
          return "-";
      }
    },
  },
  props: {
    businessType:{
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      mechanismOptions: [
        // {
        //   value: null,
        //   label: "全部",
        // },
      ],
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        createTime: "", //申请时间
        receiveTime: "", //领取时间
        productIds: "", //标准库ID
        pictureVersionType:null, //任务类型
        source:1, //来源
        auditStatus: 0, //审核状态
        createUser: "", //申请人
        applyCode: '', //单据编号
        mixtureProduct: '', //商品编码
        bindMechanism: [] //所属机构

      },
      tableLoading: false,
      tableData: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm()
    this.getOrganizeList()
  },
  mounted() {},
  methods: {

    

    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.mechanismOptions.length) {
        this.formData.bindMechanism.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.mechanismOptions.length
      ) {
        console.log(this.formData.bindMechanism)
        this.formData.bindMechanism = this.formData.bindMechanism.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.formData.bindMechanism = [];
      }
    },
    selectAll1() {
      if (this.formData.bindMechanism.length < this.mechanismOptions.length) {
        this.formData.bindMechanism = [];
        this.mechanismOptions.map((item) => {
          this.formData.bindMechanism.push(item.value);
        });
        this.formData.bindMechanism.unshift("全选");
      } else {
        this.formData.bindMechanism = [];
      }
    },

    queryList() {
      this.searchForm()
    },
    /**
     * 查询机构列表
     */
    getOrganizeList() {
      getMechanismList({
      codeType: "dept",
    })
      .then((resp) => {
        if (resp.retCode === 0) {
          resp.data.forEach((item) => {
            if (item.mechanismId) {
              this.mechanismOptions.push({
                value: item.mechanismId,
                label: item.mechanismName,
              });
            }
          });
        }
      })
      .catch((err) => {
        console.log(err);
      })
    },
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.createTimeStart  = param.createTime ? param.createTime[0] : null;
        param.createTimeEnd = param.createTime ? param.createTime[1] : null;
        param.preReceiveTimeStart = param.receiveTime ? param.receiveTime[0] : null
        param.preReceiveTimeEnd = param.receiveTime ? param.receiveTime[1] : null
        param.auditStatus = param.auditStatus === '' ? null : param.auditStatus
        param.productIds = param.productIds ? param.productIds : null
        param.createUser = param.createUser ? param.createUser : null
        param.applyCode = param.applyCode ? param.applyCode : null
        param.mixtureProduct = param.mixtureProduct ? param.mixtureProduct : null
        param.businessType = param.businessType ? param.businessType : null
        delete param.createTime;
        delete param.receiveTime

       if (param.bindMechanism[0] === '全选') {
          param.bindMechanism.shift()
          param.bindMechanism = param.bindMechanism.join()
        } else {
          param.bindMechanism = param.bindMechanism.join()
        }

        let res = await getAuditTaskList(param);
        this.tableLoading = false;
        if (res.success) {
        this.tableData = res.data.list;
        this.total = res.data.total;
        } else {
        this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
        });
        }
      } catch (error) {
        console.error(error);
      }
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        createTime: "",
        productIds: "", //标准库ID
        pictureVersionType:null, //任务类型
        source:1, //来源
        auditStatus: 0, //审核状态
        createUser: "",
        bindMechanism: []
      };
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    //审核任务
    auditTask(row, seq) {
      // this.$refs.dragImg.openDlg(
      //   this.tableData,
      //   +seq-1
      // )
      try {
        parent.CreateTab(
          `../static/dist/index.html#/productPicture/wmsImageModeration?id=${row.id}&skuIndex:${0}`,
          "待精修任务列表处理"
        );
      } catch {
        this.$router.push({
          path: "/productPicture/wmsImageModeration",
          query: {
            id: row.id,
            skuIndex: 0,
          },
        });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.dialog-wrap /deep/ {
  .el-dialog__body {
    height: 500px;
    overflow-y: scroll;
  }
  .el-form-item {
    display: flex;
    height: 40px;
    padding: 0 50px;
    .el-form-item__content {
      width: 100%;
      position: relative;
      .usePictureStatus-tip {
        color: #f56c6c;
        position: absolute;
        top: 25px;
      }
    }
    .btn-wrap {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
  .el-form-item__label {
    width: 200px;
  }
}
.task-to-be-film-container {
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
  .tab-wrap {
    padding: 0 15px;
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    padding: 0 15px;
  }
}
.img-default {
  height: 400px;
  width: 70%;
  margin: 0 auto;
  background: #ccc;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .icon {
    font-size: 64px;
    color: #fff;
    margin-bottom: 10px;
  }
}
.img-box {
  display: flex;
  justify-content: center;
  height: 400px;
  width: 70%;
  margin: 0 auto;
  .img-xt-list {
    width: 130px;
    padding-left: 50px;
    display: flex;
    flex-direction: column;
    .btn {
      height: 20px;
      text-align: center;
      line-height: 20px;
      background: #ccc;
      color: #fff;
      width: 100%;
      cursor: pointer;
      &.el-icon-arrow-up {
        margin-bottom: 5px;
      }
      &.el-icon-arrow-down {
        margin-top: 5px;
      }
    }
    .img-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      .list {
        height: 25%;
        width: 80px;
        padding: 5px 0;
        .img-wrap {
          height: 100%;
          width: 100%;
          border: 6px solid #ccc;
          cursor: pointer;
          &.active {
            border: 6px solid rgba(245, 108, 108, 0.5);
          }
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .img-dt {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    .title-wrap {
      height: 50px;
      font-size: 22px;
      font-weight: bold;
      padding-bottom: 10px;
      display: flex;
      align-items: center;
      .tip {
        color: #ccc;
        font-size: 16px;
        text-align: right;
        font-weight: normal;
        padding: 0px 10px 0px 5px;
      }
      .delete {
        color: #f56c6c;
        cursor: pointer;
        font-size: 12px;
        .el-icon-delete {
          font-size: 20px;
        }
      }
    }
    .img-wrap {
      flex: 1;
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      position: relative;
      img {
        border: 6px solid #ccc;
        height: auto;
        width: auto;
        max-height: 100%;
        max-width: 100%;
      }
    }
  }
}

/**
     * 按钮组
     */
.btn-group-wrap {
  display: flex;
  align-items: center;
  padding: 20px 0;
  .btn-wrap /deep/ {
    display: flex;
    padding-left: 30px;
    .el-button {
      margin: 0 5px;
    }
  }
  .tab-wrap /deep/ {
    .el-tabs__header {
      margin-bottom: 0px;
    }
  }
}

.el-link.el-link--primary.is-disabled {
  color: #bababa;
}
.upload-img-wrap {
  display: flex;
  img {
    height: 50px;
    width: 60px;
    padding-right: 10px;
  }
}
</style>